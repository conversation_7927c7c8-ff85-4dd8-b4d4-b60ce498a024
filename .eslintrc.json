{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"@typescript-eslint/naming-convention": ["warn", {"selector": "default", "format": ["camelCase"]}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "parameter", "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "memberLike", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase"]}], "@typescript-eslint/semi": "warn", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off"}, "ignorePatterns": ["out", "dist", "**/*.d.ts"]}