# AutoCode VSCode Extension - Project Structure

This document outlines the planned directory structure and key files for the AutoCode VSCode Extension.

**Project Directory Structure:**

- **.vscode/** - VSCode configuration
- **dist/** - Compiled extension code
- **models/** - AI models
  - **codegen-350M-mono/** - CodeGen model files
- **node_modules/** - Node.js dependencies
- **resources/** - Icons and other static resources
- **src/** - Source code
  - **api/** - API interfaces
    - **modelInterface.ts** - Interface to the CodeGen model
    - **indexer.ts** - Codebase indexing functionality
    - **webAccess.ts** - Internet access functionality
  - **commands/** - Command implementations
    - **chatCommands.ts** - Chat mode commands
    - **agentCommands.ts** - Agent mode commands
    - **contextCommands.ts** - Context management commands
  - **modes/** - Mode implementations
    - **chatMode.ts** - Chat mode implementation
    - **agentMode.ts** - Agent mode implementation
    - **agentAutoMode.ts** - Agent Auto mode implementation
  - **services/** - Core services
    - **modelService.ts** - Model loading and inference
    - **indexService.ts** - Codebase indexing service
    - **contextService.ts** - Context management service
    - **terminalService.ts** - Terminal interaction service
    - **mcpService.ts** - MCP integration service
    - **storageService.ts** - Conversation storage service
  - **ui/** - UI components
    - **chatView.ts** - Chat interface
    - **sidebarView.ts** - Sidebar view
    - **contextView.ts** - Context management view
    - **promptView.ts** - Prompt input and enhancement
  - **utils/** - Utility functions
    - **modelUtils.ts** - Model-related utilities
    - **fsUtils.ts** - File system utilities
    - **promptUtils.ts** - Prompt processing utilities
  - **extension.ts** - Extension entry point
  - **test/** - Tests
- **.eslintrc.json** - ESLint configuration
- **.gitignore** - Git ignore file
- **package.json** - NPM package configuration
- **tsconfig.json** - TypeScript configuration
- **webpack.config.js** - Webpack configuration
- **CHANGELOG.md** - Change log
- **LICENSE** - License file
- **README.md** - Project documentation
- **docs/** - Detailed documentation
  - **CODEBASE_INDEXING.md** - Codebase indexing documentation
  - **INTERNET_ACCESS.md** - Internet access documentation
  - **MCP_INTEGRATION.md** - MCP integration documentation
  - **OPERATIONAL_MODES.md** - Operational modes documentation
  - **TERMINAL_MANAGEMENT.md** - Terminal management documentation

## Key Components

### Model Integration
- `src/api/modelInterface.ts`: Interface to the CodeGen-350M-Mono model
- `src/services/modelService.ts`: Service for model loading and inference

### Codebase Indexing
- `src/api/indexer.ts`: Core indexing functionality
- `src/services/indexService.ts`: Service for managing codebase indexing

### Mode Implementations
- `src/modes/chatMode.ts`: Implementation of Chat mode
- `src/modes/agentMode.ts`: Implementation of Agent mode
- `src/modes/agentAutoMode.ts`: Implementation of Agent Auto mode

### UI Components
- `src/ui/chatView.ts`: Chat interface implementation
- `src/ui/sidebarView.ts`: Sidebar view implementation
- `src/ui/contextView.ts`: Context management view
- `src/ui/promptView.ts`: Prompt input and enhancement UI

### Services
- `src/services/contextService.ts`: Context management service
- `src/services/terminalService.ts`: Terminal interaction service
- `src/services/mcpService.ts`: MCP integration service
- `src/services/storageService.ts`: Conversation storage service

### Commands
- `src/commands/chatCommands.ts`: Chat mode commands
- `src/commands/agentCommands.ts`: Agent mode commands
- `src/commands/contextCommands.ts`: Context management commands

## Implementation Priorities

1. Basic extension setup and UI components
2. Model integration and inference
3. Chat mode functionality
4. Codebase indexing
5. Context management
6. Agent mode with confirmation flow
7. Conversation storage
8. Terminal integration
9. Agent Auto mode
10. MCP integration
11. Internet access
12. Prompt enhancement
