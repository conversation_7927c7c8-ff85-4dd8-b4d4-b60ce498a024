# AutoCode VSCode Extension - Project Structure

This document outlines the actual directory structure and key files for the AutoCode VSCode Extension.

## Project Directory Structure

```
autocode-vscode-extension/
├── .vscode/                    # VSCode configuration
│   ├── launch.json            # Debug configuration
│   ├── settings.json          # Workspace settings
│   └── tasks.json             # Build tasks
├── dist/                      # Compiled extension code (webpack output)
├── out/                       # TypeScript compilation output
├── models/                    # AI models directory
│   ├── codegen-350M-mono/     # CodeGen model files
│   └── README.md              # Model setup instructions
├── node_modules/              # Node.js dependencies
├── src/                       # Source code
│   ├── api/                   # Core API interfaces
│   │   ├── indexer.ts         # Codebase indexing functionality
│   │   ├── modelInterface.ts  # Interface to the CodeGen model
│   │   └── webAccess.ts       # Internet access functionality
│   ├── common/                # Shared utilities and constants
│   │   ├── config.ts          # Configuration management
│   │   ├── constants.ts       # Application constants
│   │   ├── logger.ts          # Logging utilities
│   │   └── utils.ts           # General utilities
│   ├── modes/                 # Operational mode implementations
│   │   ├── agentAutoMode/     # Agent Auto mode (complex)
│   │   │   ├── agentAutoActionExecutor.ts
│   │   │   ├── agentAutoMode.ts
│   │   │   ├── agentAutoPlanner.ts
│   │   │   └── agentAutoUI.ts
│   │   ├── agentMode.ts       # Agent mode implementation
│   │   └── chatMode.ts        # Chat mode implementation
│   ├── services/              # Core business logic services
│   │   ├── contextService/    # Context management
│   │   │   └── contextService.ts
│   │   ├── mcpService/        # MCP integration
│   │   │   └── mcpService.ts
│   │   ├── storageService/    # Data persistence
│   │   │   └── storageService.ts
│   │   ├── terminalService/   # Terminal integration
│   │   │   └── terminalService.ts
│   │   ├── indexService.ts    # Codebase indexing service
│   │   └── modelService.ts    # Model management service
│   ├── types/                 # TypeScript type definitions
│   │   └── index.ts           # Centralized type exports
│   └── extension.ts           # Extension entry point
├── docs/                      # Comprehensive documentation
│   ├── API_REFERENCE.md       # Technical API documentation
│   ├── CODEBASE_INDEXING.md   # Indexing system documentation
│   ├── CONFIGURATION.md       # Configuration guide
│   ├── DEVELOPMENT.md         # Development and contribution guide
│   ├── INSTALLATION.md        # Installation instructions
│   ├── INTERNET_ACCESS.md     # Web access documentation
│   ├── MCP_INTEGRATION.md     # MCP integration documentation
│   ├── OPERATIONAL_MODES.md   # Operational modes documentation
│   ├── TERMINAL_MANAGEMENT.md # Terminal integration documentation
│   ├── TROUBLESHOOTING.md     # Common issues and solutions
│   └── USER_GUIDE.md          # Complete user guide
├── .eslintrc.json             # ESLint configuration
├── .gitignore                 # Git ignore patterns
├── CHANGELOG.md               # Version history and changes
├── LICENSE                    # MIT license
├── package.json               # NPM package configuration
├── PROJECT_STRUCTURE.md       # This file
├── README.md                  # Main project documentation
├── tsconfig.json              # TypeScript configuration
└── webpack.config.js          # Webpack build configuration
```

## Key Components

### Core API Layer (`src/api/`)
- **`modelInterface.ts`**: Direct interface to the CodeGen-350M-Mono model
- **`indexer.ts`**: Core codebase indexing and querying functionality
- **`webAccess.ts`**: Internet access for documentation and examples

### Service Layer (`src/services/`)
- **`modelService.ts`**: High-level model management and inference
- **`indexService.ts`**: Codebase indexing service with progress tracking
- **`contextService/`**: Context item management and persistence
- **`terminalService/`**: Terminal command execution and safety
- **`storageService/`**: Data persistence and conversation storage
- **`mcpService/`**: Model Context Protocol integration

### Operational Modes (`src/modes/`)
- **`chatMode.ts`**: Interactive Q&A without code modifications
- **`agentMode.ts`**: AI actions with user confirmation workflow
- **`agentAutoMode/`**: Fully autonomous operation with complex planning
  - `agentAutoMode.ts`: Main mode controller
  - `agentAutoPlanner.ts`: Task planning and breakdown
  - `agentAutoActionExecutor.ts`: Action execution engine
  - `agentAutoUI.ts`: User interface management

### Shared Infrastructure (`src/common/`)
- **`config.ts`**: Configuration management and settings
- **`constants.ts`**: Application-wide constants and enums
- **`logger.ts`**: Centralized logging system
- **`utils.ts`**: General utility functions
- **`types/`**: TypeScript type definitions and interfaces

### Extension Entry Point
- **`extension.ts`**: Main extension activation and command registration

## Architecture Patterns

### Service-Oriented Architecture
- Each major feature is implemented as a service
- Services are loosely coupled and independently testable
- Dependency injection for service management

### Event-Driven Communication
- Services communicate through events and callbacks
- Minimal direct dependencies between components
- Centralized event handling in extension.ts

### Modular Design
- Features can be enabled/disabled independently
- Clear separation of concerns
- Easy to extend with new functionality

## Build and Development

### TypeScript Compilation
- Source files compiled to `out/` directory
- Strict type checking enabled
- Modern ES2020 target

### Webpack Bundling
- Production builds bundled to `dist/` directory
- Code splitting for optimal loading
- External dependencies properly handled

### Development Workflow
1. `npm run compile` - TypeScript compilation (outputs to out/)
2. `npm run build` - Development webpack build (outputs to dist/)
3. `npm run watch` - Watch mode for development
4. `F5` in VS Code - Launch extension development host
5. `npm run verify` - Comprehensive build verification
6. `npm run build:prod` - Production webpack build
7. `vsce package` - Create distributable .vsix file

### Build Verification
The project includes a comprehensive build verification script (`npm run verify`) that:
- Checks all prerequisite files
- Installs dependencies if needed
- Cleans previous builds
- Runs TypeScript compilation
- Runs webpack bundling
- Performs linting checks
- Verifies output file integrity
- Reports build statistics

## Documentation Structure

### User Documentation
- **README.md**: Project overview and quick start
- **INSTALLATION.md**: Detailed setup instructions
- **USER_GUIDE.md**: Comprehensive usage guide
- **CONFIGURATION.md**: Settings and customization
- **TROUBLESHOOTING.md**: Common issues and solutions

### Technical Documentation
- **API_REFERENCE.md**: Technical API documentation
- **DEVELOPMENT.md**: Development and contribution guide
- **PROJECT_STRUCTURE.md**: This file - project organization

### Feature Documentation
- **OPERATIONAL_MODES.md**: Detailed mode explanations
- **CODEBASE_INDEXING.md**: Indexing system details
- **TERMINAL_MANAGEMENT.md**: Terminal integration
- **MCP_INTEGRATION.md**: Model Context Protocol
- **INTERNET_ACCESS.md**: Web access capabilities

## Configuration Files

### Build Configuration
- **`tsconfig.json`**: TypeScript compiler options
- **`webpack.config.js`**: Webpack bundling configuration
- **`.eslintrc.json`**: Code linting rules

### VSCode Configuration
- **`package.json`**: Extension manifest and dependencies
- **`.vscode/launch.json`**: Debug configurations
- **`.vscode/settings.json`**: Workspace settings

### Project Management
- **`.gitignore`**: Git ignore patterns
- **`CHANGELOG.md`**: Version history
- **`LICENSE`**: MIT license terms

## Implementation Status

### ✅ Completed Components
- Core extension architecture
- All three operational modes
- Codebase indexing system
- Context management
- Terminal integration
- Internet access
- MCP integration
- Configuration system
- Comprehensive documentation

### 🔧 Areas for Enhancement
- UI/UX improvements
- Performance optimizations
- Additional language support
- Advanced debugging features
- Team collaboration features

This structure provides a solid foundation for a maintainable, extensible VSCode extension with clear separation of concerns and comprehensive documentation.
