/**
 * Logging functionality for the extension
 */

import * as vscode from 'vscode';

// Log levels
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

// Current log level
let currentLogLevel = LogLevel.INFO;

// Output channel
let outputChannel: vscode.OutputChannel | null = null;

/**
 * Initialize the logger
 * @param context The extension context
 */
export function initialize(context: vscode.ExtensionContext): void {
    // Create output channel
    outputChannel = vscode.window.createOutputChannel('AutoCode');
    
    // Add to disposables
    context.subscriptions.push(outputChannel);
    
    // Log initialization
    info('Logger initialized');
}

/**
 * Set the log level
 * @param level The log level
 */
export function setLogLevel(level: LogLevel): void {
    currentLogLevel = level;
    info(`Log level set to ${LogLevel[level]}`);
}

/**
 * Get the log level
 */
export function getLogLevel(): LogLevel {
    return currentLogLevel;
}

/**
 * Log a debug message
 * @param message The message
 * @param data Additional data
 */
export function debug(message: string, data?: any): void {
    log(LogLevel.DEBUG, message, data);
}

/**
 * Log an info message
 * @param message The message
 * @param data Additional data
 */
export function info(message: string, data?: any): void {
    log(LogLevel.INFO, message, data);
}

/**
 * Log a warning message
 * @param message The message
 * @param data Additional data
 */
export function warn(message: string, data?: any): void {
    log(LogLevel.WARN, message, data);
}

/**
 * Log an error message
 * @param message The message
 * @param error The error
 */
export function error(message: string, error?: any): void {
    log(LogLevel.ERROR, message, error);
}

/**
 * Log a message
 * @param level The log level
 * @param message The message
 * @param data Additional data
 */
function log(level: LogLevel, message: string, data?: any): void {
    // Check if the level is enabled
    if (level < currentLogLevel) {
        return;
    }
    
    // Get the timestamp
    const timestamp = new Date().toISOString();
    
    // Get the level name
    const levelName = LogLevel[level].padEnd(5);
    
    // Format the message
    let formattedMessage = `[${timestamp}] [${levelName}] ${message}`;
    
    // Add data if provided
    if (data !== undefined) {
        if (data instanceof Error) {
            formattedMessage += `\n${data.stack || data.message}`;
        } else if (typeof data === 'object') {
            try {
                formattedMessage += `\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                formattedMessage += `\n[Object]`;
            }
        } else {
            formattedMessage += `\n${data}`;
        }
    }
    
    // Log to output channel
    if (outputChannel) {
        outputChannel.appendLine(formattedMessage);
    }
    
    // Log to console
    switch (level) {
        case LogLevel.DEBUG:
            console.debug(formattedMessage);
            break;
        case LogLevel.INFO:
            console.info(formattedMessage);
            break;
        case LogLevel.WARN:
            console.warn(formattedMessage);
            break;
        case LogLevel.ERROR:
            console.error(formattedMessage);
            break;
    }
}

/**
 * Show the output channel
 */
export function show(): void {
    if (outputChannel) {
        outputChannel.show();
    }
}

/**
 * Hide the output channel
 */
export function hide(): void {
    if (outputChannel) {
        outputChannel.hide();
    }
}

/**
 * Clear the output channel
 */
export function clear(): void {
    if (outputChannel) {
        outputChannel.clear();
    }
}
