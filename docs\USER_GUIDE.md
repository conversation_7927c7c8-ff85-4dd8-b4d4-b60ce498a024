# AutoCode VSCode Extension - User Guide

## Table of Contents
1. [Installation](#installation)
2. [Getting Started](#getting-started)
3. [Operational Modes](#operational-modes)
   - [Chat Mode](#chat-mode)
   - [Agent Mode](#agent-mode)
   - [Agent Auto Mode](#agent-auto-mode)
4. [Codebase Indexing](#codebase-indexing)
5. [Context Management](#context-management)
6. [Terminal Integration](#terminal-integration)
7. [Internet Access](#internet-access)
8. [Configuration](#configuration)
9. [Keyboard Shortcuts](#keyboard-shortcuts)
10. [Troubleshooting](#troubleshooting)

## Installation

### Prerequisites
- VSCode version 1.60.0 or higher
- Minimum 4GB RAM
- 1GB disk space for model storage
- Windows/macOS/Linux compatible

### Installation Steps
1. Download the latest `.vsix` file from the releases page
2. Open VSCode
3. Go to the Extensions view (Ctrl+Shift+X)
4. Click on the "..." menu in the top-right corner
5. Select "Install from VSIX..."
6. Navigate to the downloaded `.vsix` file and select it
7. Restart VSCode when prompted

### Manual Installation (for developers)
1. Clone the repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build`
4. Press F5 to run in development mode, or
5. Package the extension: `vsce package`
6. Install the generated `.vsix` file

## Getting Started

### First Launch
When you first launch VSCode after installing AutoCode, the extension will:
1. Initialize the CodeGen-350M-Mono model
2. Begin indexing your workspace (if enabled)
3. Show status indicators in the status bar

### Status Bar Indicators
The status bar contains several indicators:
- **AutoCode**: Main menu access
- **Model Status**: Shows the current state of the model (Loading, Ready, Error)
- **Index Status**: Shows the current state of the codebase index
- **Agent Status**: Shows the current state of the Agent mode
- **Agent Auto Status**: Shows the current state of the Agent Auto mode

### Command Palette
Access AutoCode commands through the Command Palette (Ctrl+Shift+P):
- `AutoCode: Start Chat Mode`
- `AutoCode: Start Agent Mode`
- `AutoCode: Start Agent Auto Mode`
- `AutoCode: Index Workspace`
- `AutoCode: Add File to Context`
- And many more...

## Operational Modes

AutoCode offers three operational modes, each with different levels of automation and interaction.

### Chat Mode

Chat mode is designed for interactive question-answering and code explanations without making changes to your codebase.

#### Starting Chat Mode
1. Click on the AutoCode icon in the status bar, or
2. Use the Command Palette: `AutoCode: Start Chat Mode`

#### Using Chat Mode
1. Type your question or request in the input box
2. Press Enter or click Send
3. View the response in the chat window
4. Continue the conversation as needed

#### Example Queries
- "Explain how the authentication system works in this codebase"
- "What does this regex pattern do?"
- "How can I implement a debounce function in JavaScript?"
- "What's the best way to handle errors in async functions?"

#### Tips for Chat Mode
- Be specific in your questions
- Reference specific files or functions when asking about your codebase
- Use code blocks for code snippets
- Ask follow-up questions to get more details

### Agent Mode

Agent mode enables the AI to perform actions on your behalf, but requires explicit confirmation for each action.

#### Starting Agent Mode
1. Use the Command Palette: `AutoCode: Start Agent Mode`
2. Enter a task description when prompted

#### Using Agent Mode
1. Describe the task you want to accomplish
2. Review the proposed plan of actions
3. Approve or reject each action individually
4. Monitor the execution of approved actions
5. Review the results

#### Example Tasks
- "Create a React component for a login form"
- "Add error handling to the user registration function"
- "Refactor the database connection code to use environment variables"
- "Set up a basic Express server with routes for users and products"

#### Tips for Agent Mode
- Start with smaller, well-defined tasks
- Review proposed actions carefully
- Provide clear, detailed instructions
- Use context management to focus on relevant files

### Agent Auto Mode

Agent Auto mode provides fully autonomous operation without requiring confirmation for each action.

#### Starting Agent Auto Mode
1. Use the Command Palette: `AutoCode: Start Agent Auto Mode`
2. Enter a task description when prompted
3. Choose whether to auto-execute actions

#### Using Agent Auto Mode
1. Describe the task you want to accomplish
2. Monitor the progress as AutoCode works autonomously
3. Use the pause, resume, or stop buttons if needed
4. Review the results when the task is complete

#### Example Tasks
- "Set up a complete React project with routing and state management"
- "Implement a REST API for the user management system"
- "Convert all CSS files to use SCSS with variables for colors and spacing"
- "Add unit tests for all utility functions in the utils directory"

#### Tips for Agent Auto Mode
- Start with well-defined, isolated tasks
- Set clear boundaries for the scope of work
- Monitor the first few operations when trying new types of tasks
- Use the pause button if you need to review progress

## Codebase Indexing

Codebase indexing analyzes your workspace to build a comprehensive understanding of your project structure, code relationships, and semantics.

### Manual Indexing
1. Use the Command Palette: `AutoCode: Index Workspace`
2. Monitor the progress in the status bar
3. View indexing results in the output panel

### Automatic Indexing
By default, AutoCode indexes your workspace:
- When the extension is first activated
- When files are created, modified, or deleted

### Configuring Indexing
Adjust indexing behavior in the settings:
- `autocode.indexing.autoIndexOnStartup`: Enable/disable automatic indexing on startup
- `autocode.indexing.excludePatterns`: Patterns to exclude from indexing
- `autocode.indexing.maxFileSize`: Maximum file size to index

### Viewing Index Status
1. Click on the Index Status indicator in the status bar
2. View statistics about the indexed codebase

## Context Management

Context management allows you to focus the AI's attention on specific parts of your codebase.

### Adding Context
Add files or selections to the context:
1. Right-click on a file in the Explorer and select `AutoCode: Add to Context`
2. Select text in an editor, right-click, and select `AutoCode: Add Selection to Context`
3. Use the Command Palette: `AutoCode: Add File to Context` or `AutoCode: Add Selection to Context`

### Managing Context
1. Use the Command Palette: `AutoCode: Show Context View`
2. View, organize, and remove context items
3. Create and switch between different contexts

### Clearing Context
1. Use the Command Palette: `AutoCode: Clear Context`
2. Confirm the action when prompted

## Terminal Integration

Terminal integration allows AutoCode to execute commands in the integrated terminal.

### Using Terminal Commands in Agent Mode
1. Start Agent Mode with a task that requires terminal commands
2. Review and approve the proposed commands
3. Monitor the execution in the terminal

### Using Terminal Commands in Agent Auto Mode
1. Start Agent Auto Mode with a task that requires terminal commands
2. AutoCode will automatically execute the necessary commands
3. Monitor the execution in the terminal

### Terminal Safety
Terminal commands are subject to safety measures:
- Command validation before execution
- Restricted commands that require explicit confirmation
- Workspace-scoped operations
- Timeout limits for long-running processes

## Internet Access

Internet access allows AutoCode to retrieve information from online resources.

### Enabling Internet Access
Internet access is enabled by default. You can control it in the settings:
- `autocode.webAccess.enabled`: Enable/disable internet access
- `autocode.webAccess.trustedSources`: List of trusted sources
- `autocode.webAccess.timeout`: Timeout for web requests

### Using Internet Access
Internet access is used automatically when:
- Answering questions about technologies not in your codebase
- Providing up-to-date information about libraries and frameworks
- Finding code examples for specific problems
- Checking package information

## Configuration

AutoCode can be configured through the VSCode settings.

### Accessing Settings
1. Open the Command Palette (Ctrl+Shift+P)
2. Type "Preferences: Open Settings (UI)"
3. Search for "autocode"

### Key Settings

#### Model Settings
- `autocode.model.path`: Path to the model directory
- `autocode.model.maxTokens`: Maximum number of tokens to generate
- `autocode.model.temperature`: Temperature for text generation
- `autocode.model.topP`: Top-p sampling for text generation
- `autocode.model.repetitionPenalty`: Repetition penalty for text generation

#### Indexing Settings
- `autocode.indexing.autoIndexOnStartup`: Automatically index on startup
- `autocode.indexing.excludePatterns`: Patterns to exclude from indexing
- `autocode.indexing.maxFileSize`: Maximum file size to index

#### Web Access Settings
- `autocode.webAccess.enabled`: Enable web access
- `autocode.webAccess.trustedSources`: Trusted sources for web access
- `autocode.webAccess.timeout`: Timeout for web access

#### UI Settings
- `autocode.ui.theme`: UI theme
- `autocode.ui.fontSize`: Font size for UI
- `autocode.ui.showStatusBar`: Show status bar items

#### Agent Settings
- `autocode.agent.autoExecute`: Automatically execute agent actions
- `autocode.agent.confirmationRequired`: Require confirmation for agent actions

#### Terminal Settings
- `autocode.terminal.shell`: Shell to use for terminal commands
- `autocode.terminal.showOnCommand`: Show terminal when executing commands

## Keyboard Shortcuts

AutoCode provides several keyboard shortcuts for common operations:

- `Ctrl+Shift+C`: Start Chat Mode
- `Ctrl+Shift+A`: Start Agent Mode
- `Ctrl+Shift+X`: Start Agent Auto Mode
- `Ctrl+Shift+I`: Index Workspace
- `Ctrl+Shift+D`: Add File to Context
- `Ctrl+Shift+S`: Add Selection to Context
- `Ctrl+Shift+P`: Optimize Prompt

You can customize these shortcuts in the Keyboard Shortcuts editor.

## Troubleshooting

### Model Issues
- **Model Not Loading**: Check if the model files are in the correct location
- **Slow Generation**: Reduce the maximum tokens or adjust other model parameters
- **Out of Memory**: Close other applications or increase available memory

### Indexing Issues
- **Indexing Slow**: Exclude large directories or increase the maximum file size
- **Indexing Fails**: Check for very large files or unsupported file types
- **Index Not Updating**: Try manually reindexing the workspace

### UI Issues
- **UI Not Showing**: Check if the extension is activated
- **UI Looks Wrong**: Try changing the UI theme or font size
- **Status Bar Items Missing**: Enable status bar items in settings

### Terminal Issues
- **Commands Not Executing**: Check terminal permissions
- **Terminal Not Showing**: Enable showing terminal on command execution
- **Command Timeout**: Increase the timeout for terminal commands

### General Issues
- **Extension Not Working**: Try reloading VSCode
- **High CPU Usage**: Reduce the indexing scope or disable automatic indexing
- **Extension Crashes**: Check the logs for error messages

If you encounter persistent issues, please report them on the GitHub repository with detailed information about the problem and steps to reproduce it.
