# Development Guide

This guide covers development setup, contribution guidelines, and technical details for the AutoCode VSCode Extension.

## Development Environment Setup

### Prerequisites
- **Node.js** 16.0 or higher
- **npm** 7.0 or higher
- **VSCode** 1.60.0 or higher
- **Git** for version control
- **TypeScript** knowledge recommended

### Initial Setup

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-username/autocode-vscode-extension.git
   cd autocode-vscode-extension
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Install Development Tools**
   ```bash
   npm install -g @vscode/vsce
   npm install -g typescript
   ```

4. **Setup Model Files**
   ```bash
   # Create model directory
   mkdir -p models/codegen-350M-mono
   # Download model files (see Installation guide)
   ```

### Development Workflow

1. **Build the Extension**
   ```bash
   # Development build with webpack
   npm run build

   # Or TypeScript compilation only
   npm run compile

   # Watch for changes during development
   npm run watch
   ```

2. **Run Extension**
   - Press `F5` in VS Code to start debugging
   - A new Extension Development Host window opens
   - Test your changes in the new window

3. **Debug Extension**
   - Set breakpoints in TypeScript files
   - Use VSCode debugger (F5 automatically builds first)
   - Check Debug Console for logs
   - The extension will automatically rebuild when files change (if using watch mode)

## Project Architecture

### Directory Structure
```
src/
├── api/                 # Core API interfaces
│   ├── modelInterface.ts
│   ├── indexer.ts
│   └── webAccess.ts
├── services/            # Business logic services
│   ├── modelService.ts
│   ├── indexService.ts
│   ├── contextService/
│   ├── terminalService/
│   ├── storageService/
│   └── mcpService/
├── modes/               # Operational modes
│   ├── chatMode.ts
│   ├── agentMode.ts
│   └── agentAutoMode/
├── common/              # Shared utilities
│   ├── config.ts
│   ├── constants.ts
│   ├── logger.ts
│   └── utils.ts
├── types/               # TypeScript type definitions
│   └── index.ts
└── extension.ts         # Main extension entry point
```

### Key Components

#### Services Layer
- **ModelService**: Manages AI model loading and inference
- **IndexService**: Handles codebase indexing and querying
- **ContextService**: Manages context items and state
- **TerminalService**: Handles terminal command execution
- **StorageService**: Manages persistent data storage

#### Modes Layer
- **ChatMode**: Interactive Q&A without code changes
- **AgentMode**: AI actions with user confirmation
- **AgentAutoMode**: Fully autonomous operation

#### API Layer
- **ModelInterface**: Direct interface to AI model
- **Indexer**: Core indexing functionality
- **WebAccess**: Internet access capabilities

## Code Style and Standards

### TypeScript Guidelines

1. **Strict Type Checking**
   ```typescript
   // Use explicit types
   function processFile(filePath: string): Promise<IndexEntry> {
     // Implementation
   }

   // Avoid 'any' type
   interface ModelResponse {
     text: string;
     tokens: number;
     processingTime: number;
   }
   ```

2. **Error Handling**
   ```typescript
   try {
     const result = await someAsyncOperation();
     return result;
   } catch (error: any) {
     logger.error('Operation failed', error);
     throw new Error(`Failed to process: ${error.message}`);
   }
   ```

3. **Async/Await Pattern**
   ```typescript
   // Preferred
   async function loadModel(): Promise<void> {
     await this.initializeModel();
     await this.loadTokenizer();
   }

   // Avoid nested promises
   ```

### Naming Conventions

- **Classes**: PascalCase (`ModelService`, `IndexEntry`)
- **Functions/Methods**: camelCase (`loadModel`, `processQuery`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_TOKENS`, `DEFAULT_TIMEOUT`)
- **Interfaces**: PascalCase with descriptive names (`ModelConfig`, `IndexResult`)

### File Organization

1. **Single Responsibility**: One main class/interface per file
2. **Logical Grouping**: Related functionality in same directory
3. **Clear Imports**: Explicit imports, avoid barrel exports
4. **Documentation**: JSDoc comments for public APIs

## Testing

### Test Structure
```
src/test/
├── suite/
│   ├── extension.test.ts
│   ├── modelService.test.ts
│   ├── indexService.test.ts
│   └── modes/
│       ├── chatMode.test.ts
│       ├── agentMode.test.ts
│       └── agentAutoMode.test.ts
└── runTest.ts
```

### Writing Tests

1. **Unit Tests**
   ```typescript
   import * as assert from 'assert';
   import { ModelService } from '../../services/modelService';

   suite('ModelService Tests', () => {
     test('should load model successfully', async () => {
       const service = new ModelService(mockContext);
       await service.initialize();
       assert.strictEqual(service.isLoaded(), true);
     });
   });
   ```

2. **Integration Tests**
   ```typescript
   suite('Extension Integration Tests', () => {
     test('should activate extension', async () => {
       const extension = vscode.extensions.getExtension('autocode.extension');
       await extension?.activate();
       assert.ok(extension?.isActive);
     });
   });
   ```

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suite
npm test -- --grep "ModelService"

# Run with coverage
npm run test:coverage
```

## Building and Packaging

### Development Build
```bash
# TypeScript compilation (outputs to out/)
npm run compile

# Development webpack build (outputs to dist/)
npm run build

# Watch mode for development (TypeScript compilation)
npm run watch
```

### Production Build
```bash
# Clean previous builds
npm run clean

# Production webpack build
npm run build:prod

# Package extension for distribution
vsce package
```

### Release Process

1. **Version Update**
   ```bash
   # Update version in package.json
   npm version patch|minor|major
   ```

2. **Build and Test**
   ```bash
   npm run build:prod
   npm test
   ```

3. **Package Extension**
   ```bash
   vsce package
   ```

4. **Create Release**
   - Tag version in Git
   - Create GitHub release
   - Upload VSIX file

## Contributing Guidelines

### Before Contributing

1. **Check Existing Issues**
   - Search for related issues
   - Comment on existing issues
   - Create new issue if needed

2. **Fork Repository**
   - Fork on GitHub
   - Clone your fork
   - Add upstream remote

### Making Changes

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow code style guidelines
   - Add tests for new functionality
   - Update documentation

3. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

### Pull Request Process

1. **Update Documentation**
   - Update relevant .md files
   - Add JSDoc comments
   - Update CHANGELOG.md

2. **Test Changes**
   ```bash
   npm test
   npm run lint
   npm run build
   npm run verify  # Comprehensive build verification
   ```

3. **Submit Pull Request**
   - Clear description of changes
   - Link to related issues
   - Include screenshots if UI changes

## Debugging

### Extension Debugging

1. **Launch Configuration**
   ```json
   {
     "name": "Run Extension",
     "type": "extensionHost",
     "request": "launch",
     "args": ["--extensionDevelopmentPath=${workspaceFolder}"]
   }
   ```

2. **Debug Console**
   - Use `console.log()` for quick debugging
   - Use VSCode debugger for complex issues
   - Check Output panel for extension logs

### Common Debug Scenarios

1. **Model Loading Issues**
   - Check model file paths
   - Verify file permissions
   - Monitor memory usage

2. **Indexing Problems**
   - Log file processing steps
   - Check exclude patterns
   - Monitor performance metrics

3. **UI Issues**
   - Inspect webview content
   - Check message passing
   - Verify event handlers

## Performance Optimization

### Best Practices

1. **Lazy Loading**
   ```typescript
   // Load components only when needed
   private async getModelService(): Promise<ModelService> {
     if (!this.modelService) {
       this.modelService = new ModelService(this.context);
       await this.modelService.initialize();
     }
     return this.modelService;
   }
   ```

2. **Caching**
   ```typescript
   // Cache expensive operations
   private cache = new Map<string, any>();

   async getResult(key: string): Promise<any> {
     if (this.cache.has(key)) {
       return this.cache.get(key);
     }
     const result = await expensiveOperation(key);
     this.cache.set(key, result);
     return result;
   }
   ```

3. **Resource Management**
   ```typescript
   // Proper cleanup
   dispose(): void {
     this.disposables.forEach(d => d.dispose());
     this.cache.clear();
     this.modelService?.dispose();
   }
   ```

## Security Considerations

### Input Validation
```typescript
function validateInput(input: string): boolean {
  // Sanitize user input
  if (!input || input.length > MAX_INPUT_LENGTH) {
    return false;
  }
  // Check for malicious patterns
  return !DANGEROUS_PATTERNS.some(pattern => pattern.test(input));
}
```

### File System Access
```typescript
// Restrict to workspace
function isPathSafe(filePath: string): boolean {
  const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  return workspaceRoot && filePath.startsWith(workspaceRoot);
}
```

### Terminal Commands
```typescript
// Validate commands before execution
function isCommandSafe(command: string): boolean {
  return !BLOCKED_COMMANDS.some(blocked => command.includes(blocked));
}
```

## Documentation

### Code Documentation
- Use JSDoc for all public APIs
- Include examples in documentation
- Document complex algorithms

### User Documentation
- Update relevant .md files
- Include screenshots for UI changes
- Provide usage examples

### API Documentation
- Generate from JSDoc comments
- Keep API reference up to date
- Document breaking changes

## Release Management

### Version Strategy
- **Major**: Breaking changes
- **Minor**: New features, backward compatible
- **Patch**: Bug fixes, backward compatible

### Release Checklist
- [ ] All tests passing
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Version bumped
- [ ] Extension packaged
- [ ] Release notes prepared
