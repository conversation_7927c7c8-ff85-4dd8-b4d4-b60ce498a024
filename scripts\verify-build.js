#!/usr/bin/env node

/**
 * Build verification script for AutoCode VSCode Extension
 * Verifies that the extension builds correctly and all required files are present
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
    if (fs.existsSync(filePath)) {
        log(`✓ ${description}`, colors.green);
        return true;
    } else {
        log(`✗ ${description}`, colors.red);
        return false;
    }
}

function runCommand(command, description) {
    try {
        log(`Running: ${description}`, colors.blue);
        execSync(command, { stdio: 'inherit' });
        log(`✓ ${description} completed successfully`, colors.green);
        return true;
    } catch (error) {
        log(`✗ ${description} failed`, colors.red);
        return false;
    }
}

function main() {
    log('AutoCode VSCode Extension - Build Verification', colors.blue);
    log('================================================', colors.blue);

    let allChecksPass = true;

    // Check prerequisites
    log('\n1. Checking prerequisites...', colors.yellow);
    allChecksPass &= checkFile('package.json', 'package.json exists');
    allChecksPass &= checkFile('tsconfig.json', 'tsconfig.json exists');
    allChecksPass &= checkFile('webpack.config.js', 'webpack.config.js exists');
    allChecksPass &= checkFile('src/extension.ts', 'Main extension file exists');

    // Check if node_modules exists
    if (!fs.existsSync('node_modules')) {
        log('\n2. Installing dependencies...', colors.yellow);
        allChecksPass &= runCommand('npm install', 'npm install');
    } else {
        log('\n2. Dependencies already installed ✓', colors.green);
    }

    // Clean previous builds
    log('\n3. Cleaning previous builds...', colors.yellow);
    if (fs.existsSync('out')) {
        fs.rmSync('out', { recursive: true, force: true });
    }
    if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
    }
    log('✓ Previous builds cleaned', colors.green);

    // Run TypeScript compilation
    log('\n4. Running TypeScript compilation...', colors.yellow);
    allChecksPass &= runCommand('npm run compile', 'TypeScript compilation');

    // Check TypeScript output
    log('\n5. Verifying TypeScript output...', colors.yellow);
    allChecksPass &= checkFile('out/extension.js', 'Compiled extension.js exists');
    allChecksPass &= checkFile('out/extension.js.map', 'Source map exists');

    // Run webpack build
    log('\n6. Running webpack build...', colors.yellow);
    allChecksPass &= runCommand('npm run build', 'Webpack development build');

    // Check webpack output
    log('\n7. Verifying webpack output...', colors.yellow);
    allChecksPass &= checkFile('dist/extension.js', 'Bundled extension.js exists');

    // Run linting
    log('\n8. Running linting...', colors.yellow);
    allChecksPass &= runCommand('npm run lint', 'ESLint check');

    // Final verification
    log('\n9. Final verification...', colors.yellow);
    
    // Check package.json main field
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.main === './out/extension.js') {
        log('✓ package.json main field points to correct file', colors.green);
    } else {
        log(`✗ package.json main field incorrect: ${packageJson.main}`, colors.red);
        allChecksPass = false;
    }

    // Check file sizes
    const outSize = fs.statSync('out/extension.js').size;
    const distSize = fs.statSync('dist/extension.js').size;
    
    log(`✓ TypeScript output size: ${(outSize / 1024).toFixed(2)} KB`, colors.green);
    log(`✓ Webpack bundle size: ${(distSize / 1024).toFixed(2)} KB`, colors.green);

    // Summary
    log('\n' + '='.repeat(50), colors.blue);
    if (allChecksPass) {
        log('🎉 All checks passed! Extension is ready for development.', colors.green);
        log('\nNext steps:', colors.blue);
        log('1. Press F5 in VS Code to start debugging', colors.reset);
        log('2. A new VS Code window will open with the extension loaded', colors.reset);
        log('3. Test the extension functionality', colors.reset);
    } else {
        log('❌ Some checks failed. Please fix the issues above.', colors.red);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { main };
