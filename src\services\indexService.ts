/**
 * Service for managing codebase indexing
 */

import * as vscode from 'vscode';
import { Indexer } from '../api/indexer';
import { IndexQuery, IndexResult, QueryResult } from '../types';

/**
 * Service for managing codebase indexing
 */
export class IndexService {
    private indexer: Indexer;
    private context: vscode.ExtensionContext;
    private statusBarItem: vscode.StatusBarItem;
    private disposables: vscode.Disposable[] = [];
    private fileWatcher: vscode.FileSystemWatcher | undefined;
    private indexingPromise: Thenable<IndexResult> | null = null;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.indexer = new Indexer();

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
        this.statusBarItem.text = '$(database) AutoCode: Not indexed';
        this.statusBarItem.tooltip = 'AutoCode Index Status';
        this.statusBarItem.command = 'autocode.showIndexStatus';
        this.disposables.push(this.statusBarItem);

        // Register commands
        this.registerCommands();

        // Show status bar item
        this.statusBarItem.show();
    }

    /**
     * Initialize the index service
     */
    public async initialize(): Promise<void> {
        // Set up file watcher
        this.setupFileWatcher();

        // Auto-index on startup if enabled
        const config = vscode.workspace.getConfiguration('autocode');
        if (config.get<boolean>('indexing.autoIndexOnStartup', true)) {
            await this.indexWorkspace();
        }
    }

    /**
     * Index the workspace
     */
    public indexWorkspace(): Thenable<IndexResult> {
        // Check if indexing is already in progress
        if (this.indexingPromise) {
            return this.indexingPromise;
        }

        // Start indexing
        this.statusBarItem.text = '$(database~spin) AutoCode: Indexing...';

        // Create progress notification
        this.indexingPromise = vscode.window.withProgress(
            {
                location: vscode.ProgressLocation.Notification,
                title: 'Indexing codebase',
                cancellable: true
            },
            async (progress, token) => {
                try {
                    // Index the workspace
                    const result = await this.indexer.indexWorkspace(progress);

                    // Update status bar
                    this.statusBarItem.text = `$(database) AutoCode: ${result.indexedFiles} files indexed`;

                    return result;
                } catch (error: any) {
                    // Update status bar
                    this.statusBarItem.text = '$(database) AutoCode: Indexing failed';

                    // Show error message
                    vscode.window.showErrorMessage(`Failed to index workspace: ${error.message}`);

                    throw error;
                } finally {
                    this.indexingPromise = null;
                }
            }
        );

        return this.indexingPromise!;
    }

    /**
     * Query the index
     * @param query The query
     */
    public async query(query: IndexQuery): Promise<QueryResult> {
        // Check if index is empty
        const stats = this.indexer.getStatistics();
        if (stats.totalFiles === 0) {
            // Ask to index the workspace
            const result = await vscode.window.showWarningMessage(
                'The codebase has not been indexed yet. Would you like to index it now?',
                'Yes',
                'No'
            );

            if (result === 'Yes') {
                await this.indexWorkspace();
            } else {
                return {
                    entries: [],
                    symbols: [],
                    totalResults: 0,
                    elapsedTime: 0
                };
            }
        }

        // Query the index
        return this.indexer.query(query);
    }

    /**
     * Set up file watcher
     */
    private setupFileWatcher(): void {
        // Dispose of existing watcher
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }

        // Create new watcher
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');

        // Handle file creation
        this.fileWatcher.onDidCreate(async (uri) => {
            // TODO: Implement incremental indexing for created files
        });

        // Handle file changes
        this.fileWatcher.onDidChange(async (uri) => {
            // TODO: Implement incremental indexing for changed files
        });

        // Handle file deletion
        this.fileWatcher.onDidDelete(async (uri) => {
            // TODO: Implement incremental indexing for deleted files
        });

        // Add to disposables
        this.disposables.push(this.fileWatcher);
    }

    /**
     * Register commands
     */
    private registerCommands(): void {
        // Register show index status command
        const showIndexStatusCommand = vscode.commands.registerCommand('autocode.showIndexStatus', () => {
            this.showIndexStatus();
        });

        // Register index workspace command
        const indexWorkspaceCommand = vscode.commands.registerCommand('autocode.indexWorkspace', async () => {
            await this.indexWorkspace();
        });

        // Register clear index command
        const clearIndexCommand = vscode.commands.registerCommand('autocode.clearIndex', () => {
            this.clearIndex();
        });

        // Add to disposables
        this.disposables.push(showIndexStatusCommand, indexWorkspaceCommand, clearIndexCommand);
    }

    /**
     * Show index status
     */
    private showIndexStatus(): void {
        const stats = this.indexer.getStatistics();
        const isIndexing = this.indexer.isIndexing();

        // Create status message
        const status = isIndexing ? 'Indexing in progress' : `${stats.totalFiles} files indexed`;
        const lastIndexTime = stats.lastIndexTime ? new Date(stats.lastIndexTime).toLocaleString() : 'Never';
        const message = `Index Status: ${status}\nLast Indexed: ${lastIndexTime}`;

        // Show information message
        vscode.window.showInformationMessage(message);
    }

    /**
     * Clear the index
     */
    private clearIndex(): void {
        // Clear the index
        this.indexer.clearIndex();

        // Update status bar
        this.statusBarItem.text = '$(database) AutoCode: Not indexed';

        // Show information message
        vscode.window.showInformationMessage('Index cleared');
    }

    /**
     * Get the indexer
     */
    public getIndexer(): Indexer {
        return this.indexer;
    }

    /**
     * Check if indexing is in progress
     */
    public isIndexing(): boolean {
        return this.indexer.isIndexing();
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        this.disposables = [];
    }
}
