# AutoCode Operational Modes

This document provides detailed information about the three operational modes available in the AutoCode VSCode Extension.

## 1. Chat Mode

Chat mode is designed for interactive question-answering and code explanations without making changes to your codebase.

### Features

- **Code Explanations**: Get detailed explanations of code snippets or concepts
- **Code Examples**: Request examples of specific programming patterns or techniques
- **Debugging Help**: Ask for help identifying issues in your code
- **Best Practices**: Learn about recommended approaches and patterns
- **Language Information**: Get information about language features and syntax

### Use Cases

- Learning new programming concepts
- Understanding unfamiliar code
- Exploring alternative approaches
- Getting quick answers to coding questions
- Receiving explanations of error messages

### Example Interactions

In Chat mode, users can ask various types of questions:
- Implementation questions about algorithms or data structures
- Explanations of complex code patterns or regular expressions
- Troubleshooting questions about common issues
- Best practices for specific technologies

AutoCode responds with detailed explanations, conceptual breakdowns, and when appropriate, suggestions for improvement—all without making changes to the codebase.

## 2. Agent Mode

Agent mode enables the AI to perform actions on your behalf, but requires explicit confirmation for each action.

### Features

- **File Operations**: Create, read, update, and delete files
- **Terminal Commands**: Execute commands in the integrated terminal
- **Code Generation**: Generate complete code files or snippets
- **Refactoring**: Suggest and implement code improvements
- **Project Setup**: Help set up project structures and configurations

### Confirmation Flow

1. User requests an action
2. AutoCode proposes a detailed plan
3. User reviews and confirms each step
4. AutoCode executes the confirmed steps
5. User can deny or modify any proposed action

### Use Cases

- Generating boilerplate code
- Setting up project configurations
- Implementing features based on descriptions
- Refactoring existing code
- Executing complex sequences of commands

### Example Interactions

In Agent mode, users can request various actions:
- Creating new files or components
- Modifying existing code
- Executing terminal commands
- Setting up configurations

AutoCode will:
1. Analyze the request and develop a detailed plan
2. Present the plan to the user for review
3. Wait for confirmation before proceeding
4. Execute each step with additional confirmations as needed
5. Provide explanations for each action taken

## 3. Agent Auto Mode

Agent Auto mode provides fully autonomous operation without requiring confirmation for each action.

### Features

- **Autonomous Execution**: Complete tasks without intervention
- **Complex Workflows**: Handle multi-step processes end-to-end
- **Continuous Operation**: Work through tasks until completion
- **Self-Correction**: Identify and fix issues during execution
- **Progress Reporting**: Provide updates on task progress

### Safety Measures

- Workspace-scoped operations only
- Read-only access to sensitive system areas
- Activity logging for all operations
- Emergency stop command
- Configurable permission boundaries

### Use Cases

- Generating complete project scaffolding
- Implementing features from specifications
- Converting code between languages
- Setting up development environments
- Batch processing or refactoring tasks

### Example Interactions

In Agent Auto mode, users can request complex tasks:
- Setting up complete project environments
- Implementing features from specifications
- Refactoring large sections of code
- Integrating new technologies or dependencies

AutoCode will:
1. Analyze the request and develop a comprehensive plan
2. Autonomously execute all necessary steps without interruption
3. Handle errors and unexpected situations adaptively
4. Make intelligent decisions based on best practices
5. Provide a detailed summary of all actions taken upon completion

## Mode Comparison

| Feature | Chat Mode | Agent Mode | Agent Auto Mode |
|---------|-----------|------------|-----------------|
| Code explanations | ✅ | ✅ | ✅ |
| Code generation | ✅ (snippets only) | ✅ | ✅ |
| File operations | ❌ | ✅ (with confirmation) | ✅ (autonomous) |
| Terminal commands | ❌ | ✅ (with confirmation) | ✅ (autonomous) |
| Project setup | ❌ | ✅ (with confirmation) | ✅ (autonomous) |
| Refactoring | ❌ | ✅ (with confirmation) | ✅ (autonomous) |
| User control | Complete | High | Minimal |
| Execution speed | N/A | Moderate | Fast |
| Complexity handling | Low | Medium | High |

## Switching Between Modes

Users can switch between modes at any time through:

1. The mode selector in the sidebar
2. Command palette commands
3. Mode-specific buttons in the chat interface

The conversation history and context are preserved when switching modes, allowing for a seamless transition between different levels of automation.

## Prompt Enhancement

All modes benefit from the prompt enhancement feature, accessible via the "magic button":

### How It Works

1. User enters a prompt or question
2. User clicks the magic button before submitting
3. AutoCode analyzes the prompt for ambiguity, vagueness, or missing context
4. AutoCode suggests improvements to make the prompt more specific and effective
5. User can accept the enhanced prompt or make further modifications

### Benefits

- **Clearer Instructions**: Helps users articulate their needs more precisely
- **Better Context**: Automatically includes relevant contextual information
- **Improved Results**: Leads to more accurate and useful responses
- **Learning Tool**: Helps users understand how to craft effective prompts

## Best Practices

### Chat Mode
- Be specific in your questions
- Provide context when asking about specific code
- Use code blocks for code snippets

### Agent Mode
- Review proposed actions carefully
- Start with smaller tasks until familiar with the system
- Provide clear, detailed instructions

### Agent Auto Mode
- Start with well-defined, isolated tasks
- Set clear boundaries for the scope of work
- Monitor the first few operations when trying new types of tasks
