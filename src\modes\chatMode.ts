/**
 * Chat mode implementation
 */

import * as vscode from 'vscode';
import { ModelService } from '../services/modelService';
import { IndexService } from '../services/indexService';

// Define chat message
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
}

// Define chat session
export interface ChatSession {
    id: string;
    name: string;
    messages: ChatMessage[];
    createdAt: number;
    updatedAt: number;
}

/**
 * Chat mode implementation
 */
export class ChatMode {
    private modelService: ModelService;
    private indexService: IndexService;
    private context: vscode.ExtensionContext;
    private currentSession: ChatSession | null = null;
    private sessions: ChatSession[] = [];
    private webviewPanel: vscode.WebviewPanel | null = null;
    private disposables: vscode.Disposable[] = [];

    constructor(
        context: vscode.ExtensionContext,
        modelService: ModelService,
        indexService: IndexService
    ) {
        this.context = context;
        this.modelService = modelService;
        this.indexService = indexService;

        // Load sessions from storage
        this.loadSessions();

        // Register commands
        this.registerCommands();
    }

    /**
     * Start a new chat session
     */
    public async startNewSession(): Promise<void> {
        // Create a new session
        const session: ChatSession = {
            id: this.generateId(),
            name: `Chat ${new Date().toLocaleString()}`,
            messages: [
                {
                    id: this.generateId(),
                    role: 'system',
                    content: 'You are AutoCode, an AI assistant that helps with coding tasks. You can answer questions about code, explain concepts, and provide examples.',
                    timestamp: Date.now()
                }
            ],
            createdAt: Date.now(),
            updatedAt: Date.now()
        };

        // Set as current session
        this.currentSession = session;

        // Add to sessions
        this.sessions.push(session);

        // Save sessions
        this.saveSessions();

        // Show chat UI
        this.showChatUI();
    }

    /**
     * Continue an existing session
     * @param sessionId The session ID
     */
    public async continueSession(sessionId: string): Promise<void> {
        // Find the session
        const session = this.sessions.find(s => s.id === sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }

        // Set as current session
        this.currentSession = session;

        // Show chat UI
        this.showChatUI();
    }

    /**
     * Send a message
     * @param content The message content
     */
    public async sendMessage(content: string): Promise<void> {
        // Check if there's a current session
        if (!this.currentSession) {
            await this.startNewSession();
        }

        // Add user message
        const userMessage: ChatMessage = {
            id: this.generateId(),
            role: 'user',
            content,
            timestamp: Date.now()
        };

        this.currentSession!.messages.push(userMessage);
        this.currentSession!.updatedAt = Date.now();

        // Update UI
        this.updateChatUI();

        // Generate response
        await this.generateResponse();

        // Save sessions
        this.saveSessions();
    }

    /**
     * Generate a response
     */
    private async generateResponse(): Promise<void> {
        if (!this.currentSession) {
            return;
        }

        try {
            // Create a placeholder message
            const assistantMessage: ChatMessage = {
                id: this.generateId(),
                role: 'assistant',
                content: 'Thinking...',
                timestamp: Date.now()
            };

            this.currentSession.messages.push(assistantMessage);

            // Update UI
            this.updateChatUI();

            // Prepare the prompt
            const prompt = this.preparePrompt();

            // Generate text
            const response = await this.modelService.generateText({
                prompt,
                maxTokens: 1024,
                temperature: 0.7
            });

            // Update the message
            assistantMessage.content = response.text;
            assistantMessage.timestamp = Date.now();
            this.currentSession.updatedAt = Date.now();

            // Update UI
            this.updateChatUI();

            // Save sessions
            this.saveSessions();
        } catch (error: any) {
            // Handle error
            if (this.currentSession.messages.length > 0) {
                const lastMessage = this.currentSession.messages[this.currentSession.messages.length - 1];
                if (lastMessage.role === 'assistant' && lastMessage.content === 'Thinking...') {
                    lastMessage.content = `Error: ${error.message}`;

                    // Update UI
                    this.updateChatUI();
                }
            }

            // Show error message
            vscode.window.showErrorMessage(`Failed to generate response: ${error.message}`);
        }
    }

    /**
     * Prepare the prompt
     */
    private preparePrompt(): string {
        if (!this.currentSession) {
            return '';
        }

        // Build the prompt from the messages
        let prompt = '';

        for (const message of this.currentSession.messages) {
            if (message.role === 'system') {
                prompt += `System: ${message.content}\n\n`;
            } else if (message.role === 'user') {
                prompt += `User: ${message.content}\n\n`;
            } else if (message.role === 'assistant') {
                if (message.content !== 'Thinking...') {
                    prompt += `Assistant: ${message.content}\n\n`;
                }
            }
        }

        prompt += 'Assistant: ';

        return prompt;
    }

    /**
     * Show the chat UI
     */
    private showChatUI(): void {
        // Check if panel already exists
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }

        // Create webview panel
        this.webviewPanel = vscode.window.createWebviewPanel(
            'autocode.chat',
            'AutoCode Chat',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // Set webview content
        this.updateChatUI();

        // Handle messages from webview
        this.webviewPanel.webview.onDidReceiveMessage(
            async (message) => {
                if (message.command === 'sendMessage') {
                    await this.sendMessage(message.content);
                } else if (message.command === 'newSession') {
                    await this.startNewSession();
                } else if (message.command === 'continueSession') {
                    await this.continueSession(message.sessionId);
                } else if (message.command === 'renameSession') {
                    this.renameSession(message.sessionId, message.name);
                } else if (message.command === 'deleteSession') {
                    this.deleteSession(message.sessionId);
                }
            },
            undefined,
            this.disposables
        );

        // Handle panel disposal
        this.webviewPanel.onDidDispose(
            () => {
                this.webviewPanel = null;
            },
            null,
            this.disposables
        );
    }

    /**
     * Update the chat UI
     */
    private updateChatUI(): void {
        if (!this.webviewPanel) {
            return;
        }

        // Create HTML content
        const html = this.getChatHtml();

        // Set webview content
        this.webviewPanel.webview.html = html;
    }

    /**
     * Get the chat HTML
     */
    private getChatHtml(): string {
        // This is a placeholder implementation
        // In a real implementation, you would create a more sophisticated UI

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AutoCode Chat</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 0;
                        margin: 0;
                    }
                    .container {
                        display: flex;
                        height: 100vh;
                    }
                    .sidebar {
                        width: 250px;
                        border-right: 1px solid var(--vscode-panel-border);
                        overflow-y: auto;
                        padding: 10px;
                    }
                    .chat {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                    }
                    .messages {
                        flex: 1;
                        overflow-y: auto;
                        padding: 10px;
                    }
                    .input {
                        border-top: 1px solid var(--vscode-panel-border);
                        padding: 10px;
                    }
                    .message {
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 5px;
                    }
                    .user {
                        background-color: var(--vscode-editor-inactiveSelectionBackground);
                        align-self: flex-end;
                    }
                    .assistant {
                        background-color: var(--vscode-editor-selectionBackground);
                        align-self: flex-start;
                    }
                    .system {
                        background-color: var(--vscode-editor-lineHighlightBackground);
                        font-style: italic;
                    }
                    .session {
                        padding: 5px;
                        margin-bottom: 5px;
                        cursor: pointer;
                        border-radius: 3px;
                    }
                    .session:hover {
                        background-color: var(--vscode-list-hoverBackground);
                    }
                    .session.active {
                        background-color: var(--vscode-list-activeSelectionBackground);
                        color: var(--vscode-list-activeSelectionForeground);
                    }
                    .new-session {
                        margin-top: 10px;
                        padding: 5px;
                        text-align: center;
                        cursor: pointer;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border-radius: 3px;
                    }
                    .new-session:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    textarea {
                        width: 100%;
                        padding: 5px;
                        border: 1px solid var(--vscode-input-border);
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        resize: none;
                    }
                    button {
                        padding: 5px 10px;
                        margin-top: 5px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 3px;
                        cursor: pointer;
                    }
                    button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="sidebar">
                        <h3>Sessions</h3>
                        ${this.sessions.map(session => `
                            <div class="session ${this.currentSession && session.id === this.currentSession.id ? 'active' : ''}" data-id="${session.id}">
                                ${session.name}
                            </div>
                        `).join('')}
                        <div class="new-session">New Chat</div>
                    </div>
                    <div class="chat">
                        <div class="messages">
                            ${this.currentSession ? this.currentSession.messages.map(message => `
                                <div class="message ${message.role}">
                                    <div><strong>${message.role.charAt(0).toUpperCase() + message.role.slice(1)}</strong></div>
                                    <div>${message.content}</div>
                                </div>
                            `).join('') : ''}
                        </div>
                        <div class="input">
                            <textarea placeholder="Type your message here..." rows="3"></textarea>
                            <button>Send</button>
                        </div>
                    </div>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();

                    // Handle send button click
                    document.querySelector('button').addEventListener('click', () => {
                        const textarea = document.querySelector('textarea');
                        const content = textarea.value.trim();

                        if (content) {
                            vscode.postMessage({
                                command: 'sendMessage',
                                content
                            });

                            textarea.value = '';
                        }
                    });

                    // Handle textarea enter key
                    document.querySelector('textarea').addEventListener('keydown', (event) => {
                        if (event.key === 'Enter' && !event.shiftKey) {
                            event.preventDefault();
                            document.querySelector('button').click();
                        }
                    });

                    // Handle session click
                    document.querySelectorAll('.session').forEach(element => {
                        element.addEventListener('click', () => {
                            const sessionId = element.dataset.id;

                            vscode.postMessage({
                                command: 'continueSession',
                                sessionId
                            });
                        });
                    });

                    // Handle new session click
                    document.querySelector('.new-session').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'newSession'
                        });
                    });

                    // Scroll to bottom
                    const messages = document.querySelector('.messages');
                    messages.scrollTop = messages.scrollHeight;
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Rename a session
     * @param sessionId The session ID
     * @param name The new name
     */
    private renameSession(sessionId: string, name: string): void {
        // Find the session
        const session = this.sessions.find(s => s.id === sessionId);
        if (!session) {
            return;
        }

        // Update the name
        session.name = name;
        session.updatedAt = Date.now();

        // Save sessions
        this.saveSessions();

        // Update UI
        this.updateChatUI();
    }

    /**
     * Delete a session
     * @param sessionId The session ID
     */
    private deleteSession(sessionId: string): void {
        // Find the session index
        const index = this.sessions.findIndex(s => s.id === sessionId);
        if (index === -1) {
            return;
        }

        // Remove the session
        this.sessions.splice(index, 1);

        // Update current session if needed
        if (this.currentSession && this.currentSession.id === sessionId) {
            this.currentSession = this.sessions.length > 0 ? this.sessions[0] : null;
        }

        // Save sessions
        this.saveSessions();

        // Update UI
        this.updateChatUI();
    }

    /**
     * Load sessions from storage
     */
    private loadSessions(): void {
        try {
            // Get sessions from storage
            const sessionsJson = this.context.globalState.get<string>('autocode.chatSessions');
            if (sessionsJson) {
                this.sessions = JSON.parse(sessionsJson);
            }
        } catch (error) {
            console.error('Failed to load chat sessions:', error);
        }
    }

    /**
     * Save sessions to storage
     */
    private saveSessions(): void {
        try {
            // Save sessions to storage
            const sessionsJson = JSON.stringify(this.sessions);
            this.context.globalState.update('autocode.chatSessions', sessionsJson);
        } catch (error) {
            console.error('Failed to save chat sessions:', error);
        }
    }

    /**
     * Generate a unique ID
     */
    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    /**
     * Register commands
     */
    private registerCommands(): void {
        // Register start chat command
        const startChatCommand = vscode.commands.registerCommand('autocode.startChat', async () => {
            await this.startNewSession();
        });

        // Add to disposables
        this.disposables.push(startChatCommand);
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        this.disposables = [];

        // Dispose of webview panel
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
            this.webviewPanel = null;
        }
    }
}
