{"name": "autocode-vscode-extension", "displayName": "AutoCode", "description": "AI-powered coding assistant using local CodeGen-350M-Mono model", "version": "0.1.0", "engines": {"vscode": "^1.60.0"}, "categories": ["Programming Languages", "Machine Learning", "Other"], "activationEvents": ["onCommand:autocode.startChat", "onCommand:autocode.startAgent", "onCommand:autocode.startAgentAuto", "onCommand:autocode.pauseAgentAuto", "onCommand:autocode.resumeAgentAuto", "onCommand:autocode.stopAgentAuto", "onCommand:autocode.indexWorkspace", "onCommand:autocode.clearIndex", "onCommand:autocode.showIndexStatus", "onCommand:autocode.addContext", "onCommand:autocode.addSelectionToContext", "onCommand:autocode.removeContext", "onCommand:autocode.clearContext", "onCommand:autocode.showModelStatus", "onCommand:autocode.reloadModel", "onCommand:autocode.unloadModel", "onCommand:autocode.optimizePrompt", "onCommand:autocode.showChatView", "onCommand:autocode.showAgentView", "onCommand:autocode.showAgentAutoView", "onCommand:autocode.showContextView", "onCommand:autocode.showSettingsView", "onView:autocode-sidebar", "onView:autocode-modes", "onView:autocode-conversations", "onView:autocode-context"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "autocode.startChat", "title": "AutoCode: Start Chat Mode"}, {"command": "autocode.startAgent", "title": "AutoCode: Start Agent Mode"}, {"command": "autocode.startAgentAuto", "title": "AutoCode: Start Agent Auto Mode"}, {"command": "autocode.pauseAgentAuto", "title": "AutoCode: Pause Agent Auto"}, {"command": "autocode.resumeAgentAuto", "title": "AutoCode: Resume Agent Auto"}, {"command": "autocode.stopAgentAuto", "title": "AutoCode: Stop Agent Auto"}, {"command": "autocode.indexWorkspace", "title": "AutoCode: Index Workspace"}, {"command": "autocode.clearIndex", "title": "AutoCode: Clear Index"}, {"command": "autocode.showIndexStatus", "title": "AutoCode: Show Index Status"}, {"command": "autocode.addContext", "title": "AutoCode: Add File to Context"}, {"command": "autocode.addSelectionToContext", "title": "AutoCode: Add Selection to Context"}, {"command": "autocode.removeContext", "title": "AutoCode: Remove from Context"}, {"command": "autocode.clearContext", "title": "AutoCode: Clear Context"}, {"command": "autocode.showModelStatus", "title": "AutoCode: Show Model Status"}, {"command": "autocode.reloadModel", "title": "AutoCode: Reload Model"}, {"command": "autocode.unloadModel", "title": "AutoCode: Unload Model"}, {"command": "autocode.optimizePrompt", "title": "AutoCode: Optimize Prompt"}, {"command": "autocode.showChatView", "title": "AutoCode: Show Chat View"}, {"command": "autocode.showAgentView", "title": "AutoCode: Show Agent View"}, {"command": "autocode.showAgentAutoView", "title": "AutoCode: Show Agent Auto View"}, {"command": "autocode.showContextView", "title": "AutoCode: Show Context View"}, {"command": "autocode.showSettingsView", "title": "AutoCode: Show Settings View"}], "viewsContainers": {"activitybar": [{"id": "autocode-sidebar", "title": "AutoCode", "icon": "resources/icon.svg"}]}, "views": {"autocode-sidebar": [{"id": "autocode-modes", "name": "Modes"}, {"id": "autocode-conversations", "name": "Conversations"}, {"id": "autocode-context", "name": "Context"}]}, "menus": {"view/title": [{"command": "autocode.indexWorkspace", "when": "view == autocode-context", "group": "navigation"}, {"command": "autocode.clearIndex", "when": "view == autocode-context", "group": "navigation"}, {"command": "autocode.addContext", "when": "view == autocode-context", "group": "navigation"}, {"command": "autocode.clearContext", "when": "view == autocode-context", "group": "navigation"}, {"command": "autocode.startChat", "when": "view == autocode-modes", "group": "navigation"}, {"command": "autocode.startAgent", "when": "view == autocode-modes", "group": "navigation"}, {"command": "autocode.startAgentAuto", "when": "view == autocode-modes", "group": "navigation"}], "editor/context": [{"command": "autocode.addContext", "group": "autocode"}, {"command": "autocode.addSelectionToContext", "when": "editorHasSelection", "group": "autocode"}, {"command": "autocode.optimizePrompt", "when": "editorHasSelection", "group": "autocode"}]}, "configuration": {"title": "AutoCode", "properties": {"autocode.model.path": {"type": "string", "default": "models/codegen-350M-mono", "description": "Path to the model directory"}, "autocode.model.maxTokens": {"type": "number", "default": 1024, "description": "Maximum number of tokens to generate"}, "autocode.model.temperature": {"type": "number", "default": 0.7, "description": "Temperature for text generation"}, "autocode.model.topP": {"type": "number", "default": 0.9, "description": "Top-p sampling for text generation"}, "autocode.model.repetitionPenalty": {"type": "number", "default": 1.1, "description": "Repetition penalty for text generation"}, "autocode.indexing.autoIndexOnStartup": {"type": "boolean", "default": true, "description": "Automatically index the workspace on startup"}, "autocode.indexing.excludePatterns": {"type": "array", "default": ["node_modules", ".git", ".vscode", "dist", "out", "build"], "description": "Patterns to exclude from indexing"}, "autocode.indexing.maxFileSize": {"type": "number", "default": 1048576, "description": "Maximum file size to index in bytes (default: 1MB)"}, "autocode.webAccess.enabled": {"type": "boolean", "default": true, "description": "Enable web access"}, "autocode.webAccess.trustedSources": {"type": "array", "default": ["developer.mozilla.org", "docs.microsoft.com", "stackoverflow.com", "github.com", "npmjs.com", "pypi.org", "reactjs.org", "angular.io", "vuejs.org", "nodejs.org", "python.org"], "description": "Trusted sources for web access"}, "autocode.webAccess.timeout": {"type": "number", "default": 10000, "description": "Timeout for web access in milliseconds"}, "autocode.ui.theme": {"type": "string", "default": "default", "enum": ["default", "dark", "light"], "description": "UI theme"}, "autocode.ui.fontSize": {"type": "number", "default": 14, "description": "Font size for UI"}, "autocode.ui.showStatusBar": {"type": "boolean", "default": true, "description": "Show status bar items"}, "autocode.agent.autoExecute": {"type": "boolean", "default": false, "description": "Automatically execute agent actions"}, "autocode.agent.confirmationRequired": {"type": "boolean", "default": true, "description": "Require confirmation for agent actions"}, "autocode.terminal.shell": {"type": "string", "default": "default", "description": "Shell to use for terminal commands"}, "autocode.terminal.showOnCommand": {"type": "boolean", "default": true, "description": "Show terminal when executing commands"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "tsc -p ./", "build": "webpack --mode development", "build:prod": "webpack --mode production --devtool hidden-source-map", "watch": "tsc -watch -p ./", "package": "webpack --mode production --devtool hidden-source-map", "test-compile": "tsc -p ./", "test-watch": "tsc -watch -p ./", "pretest": "npm run test-compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "clean": "<PERSON><PERSON><PERSON> out dist", "verify": "node scripts/verify-build.js"}, "devDependencies": {"@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "14.x", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "eslint": "^7.27.0", "glob": "^7.1.7", "mocha": "^8.4.0", "rimraf": "^3.0.2", "typescript": "^4.3.2", "vscode-test": "^1.5.2", "webpack": "^5.38.1", "webpack-cli": "^4.7.0", "ts-loader": "^9.2.2"}, "dependencies": {"node-fetch": "^2.6.1"}}