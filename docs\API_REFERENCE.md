# API Reference

This document provides technical reference for the AutoCode VSCode Extension APIs and interfaces.

## Core Interfaces

### ModelInterface

The main interface for interacting with the CodeGen-350M-Mono model.

#### Methods

##### `loadModel(config: ModelConfig): Promise<void>`
Loads the AI model with specified configuration.

**Parameters:**
- `config: ModelConfig` - Model configuration object

**Returns:** `Promise<void>`

**Example:**
```typescript
const config: ModelConfig = {
  modelPath: "models/codegen-350M-mono",
  maxTokens: 1024,
  temperature: 0.7,
  topP: 0.9,
  repetitionPenalty: 1.1
};
await modelInterface.loadModel(config);
```

##### `generateText(request: ModelRequest): Promise<ModelResponse>`
Generates text based on the provided prompt.

**Parameters:**
- `request: ModelRequest` - Request containing prompt and generation parameters

**Returns:** `Promise<ModelResponse>` - Generated text and metadata

**Example:**
```typescript
const request: ModelRequest = {
  prompt: "Create a React component",
  maxTokens: 512,
  temperature: 0.7
};
const response = await modelInterface.generateText(request);
```

##### `isLoaded(): boolean`
Checks if the model is currently loaded and ready.

**Returns:** `boolean` - True if model is loaded

##### `unloadModel(): Promise<void>`
Unloads the model from memory.

**Returns:** `Promise<void>`

### Indexer

Handles codebase indexing and querying functionality.

#### Methods

##### `indexWorkspace(progress?: vscode.Progress): Promise<IndexResult>`
Indexes the current workspace.

**Parameters:**
- `progress?: vscode.Progress` - Optional progress reporter

**Returns:** `Promise<IndexResult>` - Indexing results and statistics

##### `indexFile(fileUri: vscode.Uri): Promise<IndexEntry>`
Indexes a specific file.

**Parameters:**
- `fileUri: vscode.Uri` - URI of the file to index

**Returns:** `Promise<IndexEntry>` - Index entry for the file

##### `query(query: IndexQuery): Promise<QueryResult>`
Queries the indexed codebase.

**Parameters:**
- `query: IndexQuery` - Query parameters

**Returns:** `Promise<QueryResult>` - Query results

**Example:**
```typescript
const query: IndexQuery = {
  text: "authentication",
  filePattern: "*.ts",
  maxResults: 10
};
const results = await indexer.query(query);
```

##### `clearIndex(): void`
Clears the entire index.

##### `getStatistics(): IndexStatistics`
Returns indexing statistics.

**Returns:** `IndexStatistics` - Current index statistics

### WebAccess

Provides internet access capabilities for retrieving external information.

#### Methods

##### `search(query: string): Promise<SearchResult[]>`
Searches for information online.

**Parameters:**
- `query: string` - Search query

**Returns:** `Promise<SearchResult[]>` - Array of search results

##### `getDocumentation(technology: string): Promise<Documentation>`
Retrieves documentation for a specific technology.

**Parameters:**
- `technology: string` - Technology name (e.g., "React", "Node.js")

**Returns:** `Promise<Documentation>` - Documentation content

##### `getCodeExamples(language: string, concept: string): Promise<CodeExample[]>`
Finds code examples for specific concepts.

**Parameters:**
- `language: string` - Programming language
- `concept: string` - Programming concept

**Returns:** `Promise<CodeExample[]>` - Array of code examples

##### `getPackageInfo(packageName: string): Promise<PackageInfo>`
Retrieves information about a package.

**Parameters:**
- `packageName: string` - Package name

**Returns:** `Promise<PackageInfo>` - Package information

## Service APIs

### ModelService

High-level service for model management.

#### Methods

##### `initialize(): Promise<void>`
Initializes the model service.

##### `generateResponse(prompt: string, context?: string): Promise<string>`
Generates a response with optional context.

**Parameters:**
- `prompt: string` - User prompt
- `context?: string` - Optional context information

**Returns:** `Promise<string>` - Generated response

##### `isReady(): boolean`
Checks if the service is ready to handle requests.

##### `getStatus(): ModelStatus`
Returns current model status.

**Returns:** `ModelStatus` - Current status

### IndexService

High-level service for indexing management.

#### Methods

##### `initialize(): Promise<void>`
Initializes the indexing service.

##### `indexWorkspace(): Promise<IndexResult>`
Indexes the current workspace.

##### `queryCodebase(query: string): Promise<QueryResult>`
Queries the indexed codebase with natural language.

**Parameters:**
- `query: string` - Natural language query

**Returns:** `Promise<QueryResult>` - Query results

##### `addFileToIndex(fileUri: vscode.Uri): Promise<void>`
Adds a specific file to the index.

##### `removeFileFromIndex(fileUri: vscode.Uri): Promise<void>`
Removes a file from the index.

### ContextService

Manages context items and state.

#### Methods

##### `addFile(filePath: string): Promise<ContextItem>`
Adds a file to the current context.

**Parameters:**
- `filePath: string` - Path to the file

**Returns:** `Promise<ContextItem>` - Created context item

##### `addSelection(selection: vscode.Selection, document: vscode.TextDocument): Promise<ContextItem>`
Adds a text selection to the context.

**Parameters:**
- `selection: vscode.Selection` - Selected text range
- `document: vscode.TextDocument` - Source document

**Returns:** `Promise<ContextItem>` - Created context item

##### `addFolder(folderPath: string, recursive?: boolean): Promise<ContextItem[]>`
Adds a folder to the context.

**Parameters:**
- `folderPath: string` - Path to the folder
- `recursive?: boolean` - Whether to include subfolders

**Returns:** `Promise<ContextItem[]>` - Created context items

##### `removeContext(itemId: string): Promise<void>`
Removes a context item.

**Parameters:**
- `itemId: string` - ID of the context item to remove

##### `clearContext(): Promise<void>`
Clears all context items.

##### `getContext(): ContextItem[]`
Returns current context items.

**Returns:** `ContextItem[]` - Array of context items

### TerminalService

Handles terminal command execution.

#### Methods

##### `executeCommand(command: string): Promise<TerminalResult>`
Executes a terminal command.

**Parameters:**
- `command: string` - Command to execute

**Returns:** `Promise<TerminalResult>` - Command execution result

##### `executeSequence(commands: string[]): Promise<TerminalResult[]>`
Executes a sequence of commands.

**Parameters:**
- `commands: string[]` - Array of commands to execute

**Returns:** `Promise<TerminalResult[]>` - Array of execution results

##### `isCommandSafe(command: string): boolean`
Checks if a command is safe to execute.

**Parameters:**
- `command: string` - Command to check

**Returns:** `boolean` - True if command is safe

## Type Definitions

### ModelConfig
```typescript
interface ModelConfig {
  modelPath: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  repetitionPenalty: number;
}
```

### ModelRequest
```typescript
interface ModelRequest {
  prompt: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  repetitionPenalty?: number;
}
```

### ModelResponse
```typescript
interface ModelResponse {
  text: string;
  tokens: number;
  processingTime: number;
}
```

### IndexEntry
```typescript
interface IndexEntry {
  uri: vscode.Uri;
  fileName: string;
  filePath: string;
  fileExtension: string;
  content: string;
  symbols: CodeSymbol[];
  lastModified: number;
}
```

### CodeSymbol
```typescript
interface CodeSymbol {
  name: string;
  kind: vscode.SymbolKind;
  range: vscode.Range;
  detail?: string;
  containerName?: string;
}
```

### IndexQuery
```typescript
interface IndexQuery {
  text?: string;
  filePattern?: string;
  symbolKind?: vscode.SymbolKind;
  maxResults?: number;
}
```

### IndexResult
```typescript
interface IndexResult {
  entries: IndexEntry[];
  totalFiles: number;
  indexedFiles: number;
  skippedFiles: number;
  elapsedTime: number;
}
```

### QueryResult
```typescript
interface QueryResult {
  entries: IndexEntry[];
  symbols: CodeSymbol[];
  totalResults: number;
  elapsedTime: number;
}
```

### ContextItem
```typescript
interface ContextItem {
  id: string;
  type: ContextType;
  name: string;
  content: string;
  metadata: ContextMetadata;
  createdAt: number;
}
```

### ContextType
```typescript
enum ContextType {
  File = 'file',
  Folder = 'folder',
  Selection = 'selection',
  Web = 'web'
}
```

### TerminalResult
```typescript
interface TerminalResult {
  command: string;
  output: string;
  exitCode: number;
  executionTime: number;
  success: boolean;
}
```

### SearchResult
```typescript
interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
}
```

### Documentation
```typescript
interface Documentation {
  title: string;
  content: string;
  url: string;
  source: string;
}
```

### CodeExample
```typescript
interface CodeExample {
  title: string;
  code: string;
  language: string;
  source: string;
  url: string;
}
```

### PackageInfo
```typescript
interface PackageInfo {
  name: string;
  version: string;
  description: string;
  author: string;
  license: string;
  dependencies: Record<string, string>;
  repository?: string;
}
```

## Events

### ModelService Events

#### `onModelLoaded`
Fired when the model is successfully loaded.

#### `onModelError`
Fired when a model error occurs.

**Event Data:** `{ error: Error }`

### IndexService Events

#### `onIndexingStarted`
Fired when indexing begins.

#### `onIndexingProgress`
Fired during indexing progress.

**Event Data:** `{ progress: number, message: string }`

#### `onIndexingCompleted`
Fired when indexing completes.

**Event Data:** `{ result: IndexResult }`

#### `onIndexingError`
Fired when indexing encounters an error.

**Event Data:** `{ error: Error }`

### ContextService Events

#### `onContextAdded`
Fired when a context item is added.

**Event Data:** `{ item: ContextItem }`

#### `onContextRemoved`
Fired when a context item is removed.

**Event Data:** `{ itemId: string }`

#### `onContextCleared`
Fired when all context is cleared.

## Error Handling

### Common Error Types

#### `ModelError`
Thrown when model operations fail.

#### `IndexError`
Thrown when indexing operations fail.

#### `ContextError`
Thrown when context operations fail.

#### `TerminalError`
Thrown when terminal operations fail.

#### `WebAccessError`
Thrown when web access operations fail.

### Error Properties

All error types include:
- `message: string` - Error description
- `code: string` - Error code
- `details?: any` - Additional error details

## Usage Examples

### Basic Model Usage
```typescript
import { ModelService } from './services/modelService';

const modelService = new ModelService(context);
await modelService.initialize();

const response = await modelService.generateResponse(
  "Create a TypeScript interface for a user",
  "This is for a web application"
);
```

### Indexing and Querying
```typescript
import { IndexService } from './services/indexService';

const indexService = new IndexService(context);
await indexService.initialize();
await indexService.indexWorkspace();

const results = await indexService.queryCodebase(
  "Find all authentication functions"
);
```

### Context Management
```typescript
import { ContextService } from './services/contextService';

const contextService = new ContextService(context, storageService);
await contextService.addFile('src/auth/login.ts');
await contextService.addSelection(selection, document);

const context = contextService.getContext();
```
