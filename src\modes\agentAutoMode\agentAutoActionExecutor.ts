/**
 * Agent Auto Action Executor
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { AgentAutoAction } from '../../types';
import { TerminalService } from '../../services/terminalService';
import * as logger from '../../common/logger';
import * as utils from '../../common/utils';

/**
 * Agent Auto Action Executor
 */
export class AgentAutoActionExecutor {
    private context: vscode.ExtensionContext;
    private terminalService: TerminalService;
    
    constructor(
        context: vscode.ExtensionContext,
        terminalService: TerminalService
    ) {
        this.context = context;
        this.terminalService = terminalService;
    }
    
    /**
     * Execute an action
     * @param action The action to execute
     */
    public async executeAction(action: AgentAutoAction): Promise<void> {
        logger.debug(`Executing action: ${action.type} - ${action.description}`);
        
        switch (action.type) {
            case 'file_create':
                await this.executeFileCreate(action);
                break;
            case 'file_update':
                await this.executeFileUpdate(action);
                break;
            case 'file_delete':
                await this.executeFileDelete(action);
                break;
            case 'terminal_command':
                await this.executeTerminalCommand(action);
                break;
            default:
                throw new Error(`Unsupported action type: ${action.type}`);
        }
    }
    
    /**
     * Execute a file create action
     * @param action The action to execute
     */
    private async executeFileCreate(action: AgentAutoAction): Promise<void> {
        const { filePath, fileContent } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = utils.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Create the directory if it doesn't exist
        const directory = path.dirname(fullPath);
        if (!fs.existsSync(directory)) {
            fs.mkdirSync(directory, { recursive: true });
        }
        
        // Check if the file already exists
        if (fs.existsSync(fullPath)) {
            throw new Error(`File already exists: ${filePath}`);
        }
        
        // Write the file
        fs.writeFileSync(fullPath, fileContent || '');
        
        // Update the action result
        action.result = `Created file: ${filePath}`;
        
        logger.info(`Created file: ${filePath}`);
    }
    
    /**
     * Execute a file update action
     * @param action The action to execute
     */
    private async executeFileUpdate(action: AgentAutoAction): Promise<void> {
        const { filePath, fileContent } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = utils.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Check if the file exists
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        // Write the file
        fs.writeFileSync(fullPath, fileContent || '');
        
        // Update the action result
        action.result = `Updated file: ${filePath}`;
        
        logger.info(`Updated file: ${filePath}`);
    }
    
    /**
     * Execute a file delete action
     * @param action The action to execute
     */
    private async executeFileDelete(action: AgentAutoAction): Promise<void> {
        const { filePath } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = utils.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Check if the file exists
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        // Delete the file
        fs.unlinkSync(fullPath);
        
        // Update the action result
        action.result = `Deleted file: ${filePath}`;
        
        logger.info(`Deleted file: ${filePath}`);
    }
    
    /**
     * Execute a terminal command action
     * @param action The action to execute
     */
    private async executeTerminalCommand(action: AgentAutoAction): Promise<void> {
        const { command, cwd } = action.details;
        
        if (!command) {
            throw new Error('Command is required');
        }
        
        // Execute the command
        const output = await this.terminalService.executeCommand(command, cwd);
        
        // Update the action result
        action.result = `Executed command: ${command}`;
        
        logger.info(`Executed command: ${command}`);
    }
}
