/**
 * Context service for managing context items
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { Context, ContextItem } from '../../types';
import { StorageService } from '../storageService';
import { STORAGE_KEYS } from '../../common/constants';
import * as logger from '../../common/logger';
import * as utils from '../../common/utils';

/**
 * Context service for managing context items
 */
export class ContextService {
    private context: vscode.ExtensionContext;
    private storageService: StorageService;
    private currentContext: Context | null = null;
    private disposables: vscode.Disposable[] = [];

    constructor(context: vscode.ExtensionContext, storageService: StorageService) {
        this.context = context;
        this.storageService = storageService;

        // Load the current context
        this.loadCurrentContext();

        logger.info('ContextService initialized');
    }

    /**
     * Load the current context
     */
    private async loadCurrentContext(): Promise<void> {
        try {
            // Get the current context ID
            const currentContextId = this.context.globalState.get<string>('autocode.currentContextId');

            if (currentContextId) {
                // Get the context
                const contextItem = await this.storageService.get<Context>(STORAGE_KEYS.CONTEXTS, currentContextId);

                if (contextItem) {
                    this.currentContext = contextItem.data;
                    logger.debug(`Loaded current context: ${this.currentContext.name}`);
                }
            }

            // If no context was loaded, create a default one
            if (!this.currentContext) {
                await this.createContext('Default Context');
            }
        } catch (error) {
            logger.error('Failed to load current context', error);

            // Create a default context
            await this.createContext('Default Context');
        }
    }

    /**
     * Create a new context
     * @param name The context name
     */
    public async createContext(name: string): Promise<Context> {
        try {
            // Create the context
            const context: Context = {
                id: utils.generateId(),
                name,
                items: [],
                createdAt: Date.now(),
                updatedAt: Date.now()
            };

            // Store the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', context, context.id));

            // Set as current context
            this.currentContext = context;

            // Save the current context ID
            await this.context.globalState.update('autocode.currentContextId', context.id);

            logger.debug(`Created context: ${name}`);

            return context;
        } catch (error) {
            logger.error(`Failed to create context: ${name}`, error);
            throw error;
        }
    }

    /**
     * Get the current context
     */
    public getCurrentContext(): Context | null {
        return this.currentContext;
    }

    /**
     * Set the current context
     * @param contextId The context ID
     */
    public async setCurrentContext(contextId: string): Promise<void> {
        try {
            // Get the context
            const contextItem = await this.storageService.get<Context>(STORAGE_KEYS.CONTEXTS, contextId);

            if (!contextItem) {
                throw new Error(`Context not found: ${contextId}`);
            }

            // Set as current context
            this.currentContext = contextItem.data;

            // Save the current context ID
            await this.context.globalState.update('autocode.currentContextId', contextId);

            logger.debug(`Set current context: ${this.currentContext.name}`);
        } catch (error) {
            logger.error(`Failed to set current context: ${contextId}`, error);
            throw error;
        }
    }

    /**
     * Get all contexts
     */
    public async getAllContexts(): Promise<Context[]> {
        try {
            // Get all context items
            const contextItems = await this.storageService.query<Context>(STORAGE_KEYS.CONTEXTS, { type: 'context' });

            // Extract the contexts
            return contextItems.map(item => item.data);
        } catch (error) {
            logger.error('Failed to get all contexts', error);
            throw error;
        }
    }

    /**
     * Delete a context
     * @param contextId The context ID
     */
    public async deleteContext(contextId: string): Promise<void> {
        try {
            // Delete the context
            await this.storageService.delete(STORAGE_KEYS.CONTEXTS, contextId);

            // If the deleted context was the current one, load another one
            if (this.currentContext && this.currentContext.id === contextId) {
                // Get all contexts
                const contexts = await this.getAllContexts();

                if (contexts.length > 0) {
                    // Set the first context as current
                    await this.setCurrentContext(contexts[0].id);
                } else {
                    // Create a new default context
                    await this.createContext('Default Context');
                }
            }

            logger.debug(`Deleted context: ${contextId}`);
        } catch (error) {
            logger.error(`Failed to delete context: ${contextId}`, error);
            throw error;
        }
    }

    /**
     * Add a file to the current context
     * @param filePath The file path
     */
    public async addFile(filePath: string): Promise<ContextItem> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Check if the file exists
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Read the file content
            const content = fs.readFileSync(filePath, 'utf8');

            // Create the context item
            const item: ContextItem = {
                id: utils.generateId(),
                type: 'file',
                name: path.basename(filePath),
                content,
                uri: vscode.Uri.file(filePath),
                createdAt: Date.now()
            };

            // Add to the current context
            this.currentContext.items.push(item);
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Added file to context: ${filePath}`);

            return item;
        } catch (error) {
            logger.error(`Failed to add file to context: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * Add a folder to the current context
     * @param folderPath The folder path
     * @param recursive Whether to add files recursively
     * @param filter A filter function for files
     */
    public async addFolder(folderPath: string, recursive = false, filter?: (filePath: string) => boolean): Promise<ContextItem[]> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Check if the folder exists
            if (!fs.existsSync(folderPath) || !fs.statSync(folderPath).isDirectory()) {
                throw new Error(`Folder not found: ${folderPath}`);
            }

            // Get all files in the folder
            const files = this.getFilesInFolder(folderPath, recursive, filter);

            // Add each file
            const items: ContextItem[] = [];

            for (const file of files) {
                try {
                    const item = await this.addFile(file);
                    items.push(item);
                } catch (error) {
                    logger.warn(`Failed to add file to context: ${file}`, error);
                }
            }

            logger.debug(`Added folder to context: ${folderPath}`);

            return items;
        } catch (error) {
            logger.error(`Failed to add folder to context: ${folderPath}`, error);
            throw error;
        }
    }

    /**
     * Add the current selection to the context
     */
    public async addSelection(): Promise<ContextItem> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                throw new Error('No active text editor');
            }

            // Get the selection
            const selection = editor.selection;
            if (selection.isEmpty) {
                throw new Error('No text selected');
            }

            // Get the selected text
            const text = editor.document.getText(selection);

            // Create the context item
            const item: ContextItem = {
                id: utils.generateId(),
                type: 'selection',
                name: `Selection from ${path.basename(editor.document.fileName)}`,
                content: text,
                uri: editor.document.uri,
                createdAt: Date.now()
            };

            // Add to the current context
            this.currentContext.items.push(item);
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Added selection to context`);

            return item;
        } catch (error) {
            logger.error('Failed to add selection to context', error);
            throw error;
        }
    }

    /**
     * Add terminal output to the context
     * @param output The terminal output
     */
    public async addTerminalOutput(output: string): Promise<ContextItem> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Create the context item
            const item: ContextItem = {
                id: utils.generateId(),
                type: 'terminal',
                name: `Terminal output ${new Date().toLocaleString()}`,
                content: output,
                createdAt: Date.now()
            };

            // Add to the current context
            this.currentContext.items.push(item);
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Added terminal output to context`);

            return item;
        } catch (error) {
            logger.error('Failed to add terminal output to context', error);
            throw error;
        }
    }

    /**
     * Add web content to the context
     * @param url The URL
     * @param content The content
     */
    public async addWebContent(url: string, content: string): Promise<ContextItem> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Create the context item
            const item: ContextItem = {
                id: utils.generateId(),
                type: 'web',
                name: `Web content from ${url}`,
                content,
                createdAt: Date.now()
            };

            // Add to the current context
            this.currentContext.items.push(item);
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Added web content to context: ${url}`);

            return item;
        } catch (error) {
            logger.error(`Failed to add web content to context: ${url}`, error);
            throw error;
        }
    }

    /**
     * Remove an item from the current context
     * @param itemId The item ID
     */
    public async removeItem(itemId: string): Promise<void> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Find the item index
            const index = this.currentContext.items.findIndex(item => item.id === itemId);

            if (index === -1) {
                throw new Error(`Item not found: ${itemId}`);
            }

            // Remove the item
            this.currentContext.items.splice(index, 1);
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Removed item from context: ${itemId}`);
        } catch (error) {
            logger.error(`Failed to remove item from context: ${itemId}`, error);
            throw error;
        }
    }

    /**
     * Clear the current context
     */
    public async clearContext(): Promise<void> {
        try {
            // Check if there's a current context
            if (!this.currentContext) {
                throw new Error('No current context');
            }

            // Clear the items
            this.currentContext.items = [];
            this.currentContext.updatedAt = Date.now();

            // Save the context
            await this.storageService.store(STORAGE_KEYS.CONTEXTS, this.storageService.createItem('context', this.currentContext, this.currentContext.id));

            logger.debug(`Cleared context: ${this.currentContext.name}`);
        } catch (error) {
            logger.error('Failed to clear context', error);
            throw error;
        }
    }

    /**
     * Get files in a folder
     * @param folderPath The folder path
     * @param recursive Whether to get files recursively
     * @param filter A filter function for files
     */
    private getFilesInFolder(folderPath: string, recursive = false, filter?: (filePath: string) => boolean): string[] {
        // Get all files in the folder
        const files: string[] = [];

        // Read the folder
        const entries = fs.readdirSync(folderPath);

        // Process each entry
        for (const entry of entries) {
            const entryPath = path.join(folderPath, entry);
            const stats = fs.statSync(entryPath);

            if (stats.isFile()) {
                // Check if the file passes the filter
                if (!filter || filter(entryPath)) {
                    files.push(entryPath);
                }
            } else if (stats.isDirectory() && recursive) {
                // Recursively get files in the subfolder
                const subfolderFiles = this.getFilesInFolder(entryPath, recursive, filter);
                files.push(...subfolderFiles);
            }
        }

        return files;
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        this.disposables = [];
    }
}
