/**
 * Service for managing the CodeGen model
 */

import * as vscode from 'vscode';
import { ModelInterface } from '../api/modelInterface';
import { ModelRequest, ModelResponse, ModelConfig } from '../types';

/**
 * Service for managing the CodeGen model
 */
export class ModelService {
    private modelInterface: ModelInterface;
    private context: vscode.ExtensionContext;
    private statusBarItem: vscode.StatusBarItem;
    private disposables: vscode.Disposable[] = [];

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.modelInterface = new ModelInterface(context);

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(sync) AutoCode: Loading...';
        this.statusBarItem.tooltip = 'AutoCode Model Status';
        this.statusBarItem.command = 'autocode.showModelStatus';
        this.disposables.push(this.statusBarItem);

        // Register commands
        this.registerCommands();

        // Show status bar item
        this.statusBarItem.show();
    }

    /**
     * Initialize the model service
     */
    public async initialize(): Promise<void> {
        try {
            this.statusBarItem.text = '$(sync~spin) AutoCode: Loading...';
            await this.modelInterface.loadModel();
            this.statusBarItem.text = '$(check) AutoCode: Ready';
        } catch (error) {
            this.statusBarItem.text = '$(error) AutoCode: Error';
            throw error;
        }
    }

    /**
     * Generate text using the model
     * @param request The model request
     */
    public async generateText(request: ModelRequest): Promise<ModelResponse> {
        try {
            this.statusBarItem.text = '$(sync~spin) AutoCode: Generating...';
            const response = await this.modelInterface.generateText(request);
            this.statusBarItem.text = '$(check) AutoCode: Ready';
            return response;
        } catch (error) {
            this.statusBarItem.text = '$(error) AutoCode: Error';
            throw error;
        }
    }

    /**
     * Update model configuration
     * @param config The new model configuration
     */
    public updateConfig(config: Partial<ModelConfig>): void {
        this.modelInterface.updateConfig(config);
    }

    /**
     * Get the current model configuration
     */
    public getConfig(): ModelConfig {
        return this.modelInterface.getConfig();
    }

    /**
     * Check if the model is loaded
     */
    public isModelLoaded(): boolean {
        return this.modelInterface.isModelLoaded();
    }

    /**
     * Register commands
     */
    private registerCommands(): void {
        // Register show model status command
        const showModelStatusCommand = vscode.commands.registerCommand('autocode.showModelStatus', () => {
            this.showModelStatus();
        });

        // Register reload model command
        const reloadModelCommand = vscode.commands.registerCommand('autocode.reloadModel', async () => {
            await this.reloadModel();
        });

        // Register unload model command
        const unloadModelCommand = vscode.commands.registerCommand('autocode.unloadModel', async () => {
            await this.unloadModel();
        });

        // Add to disposables
        this.disposables.push(showModelStatusCommand, reloadModelCommand, unloadModelCommand);
    }

    /**
     * Show model status
     */
    private showModelStatus(): void {
        const config = this.modelInterface.getConfig();
        const isLoaded = this.modelInterface.isModelLoaded();

        // Create status message
        const status = isLoaded ? 'Loaded' : 'Not loaded';
        const message = `Model Status: ${status}\nModel Path: ${config.modelPath}\nMax Tokens: ${config.maxTokens}\nTemperature: ${config.temperature}\nTop P: ${config.topP}\nRepetition Penalty: ${config.repetitionPenalty}`;

        // Show information message
        vscode.window.showInformationMessage(message);
    }

    /**
     * Reload the model
     */
    private async reloadModel(): Promise<void> {
        try {
            this.statusBarItem.text = '$(sync~spin) AutoCode: Reloading...';

            // Unload the model
            await this.modelInterface.unloadModel();

            // Load the model
            await this.modelInterface.loadModel();

            this.statusBarItem.text = '$(check) AutoCode: Ready';
            vscode.window.showInformationMessage('Model reloaded successfully');
        } catch (error: any) {
            this.statusBarItem.text = '$(error) AutoCode: Error';
            vscode.window.showErrorMessage(`Failed to reload model: ${error.message}`);
        }
    }

    /**
     * Unload the model
     */
    private async unloadModel(): Promise<void> {
        try {
            this.statusBarItem.text = '$(sync~spin) AutoCode: Unloading...';

            // Unload the model
            await this.modelInterface.unloadModel();

            this.statusBarItem.text = '$(warning) AutoCode: Not loaded';
            vscode.window.showInformationMessage('Model unloaded successfully');
        } catch (error: any) {
            this.statusBarItem.text = '$(error) AutoCode: Error';
            vscode.window.showErrorMessage(`Failed to unload model: ${error.message}`);
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Unload the model
        this.modelInterface.unloadModel().catch(console.error);

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        this.disposables = [];
    }
}
