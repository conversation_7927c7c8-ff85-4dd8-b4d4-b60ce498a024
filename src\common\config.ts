/**
 * Configuration management for the extension
 */

import * as vscode from 'vscode';
import { CONFIG, DEFAULT_CONFIG } from './constants';

/**
 * Get a configuration value
 * @param key The configuration key
 * @param defaultValue The default value
 */
export function getConfig<T>(key: string, defaultValue?: T): T {
    const config = vscode.workspace.getConfiguration();
    return config.get<T>(key, defaultValue as T);
}

/**
 * Set a configuration value
 * @param key The configuration key
 * @param value The value to set
 * @param global Whether to set the value globally
 */
export async function setConfig<T>(key: string, value: T, global: boolean = true): Promise<void> {
    const config = vscode.workspace.getConfiguration();
    await config.update(key, value, global);
}

/**
 * Get the model path
 */
export function getModelPath(): string {
    return getConfig<string>(CONFIG.MODEL_PATH, DEFAULT_CONFIG.MODEL_PATH);
}

/**
 * Get the model max tokens
 */
export function getModelMaxTokens(): number {
    return getConfig<number>(CONFIG.MODEL_MAX_TOKENS, DEFAULT_CONFIG.MODEL_MAX_TOKENS);
}

/**
 * Get the model temperature
 */
export function getModelTemperature(): number {
    return getConfig<number>(CONFIG.MODEL_TEMPERATURE, DEFAULT_CONFIG.MODEL_TEMPERATURE);
}

/**
 * Get the model top P
 */
export function getModelTopP(): number {
    return getConfig<number>(CONFIG.MODEL_TOP_P, DEFAULT_CONFIG.MODEL_TOP_P);
}

/**
 * Get the model repetition penalty
 */
export function getModelRepetitionPenalty(): number {
    return getConfig<number>(CONFIG.MODEL_REPETITION_PENALTY, DEFAULT_CONFIG.MODEL_REPETITION_PENALTY);
}

/**
 * Get whether to auto-index on startup
 */
export function getAutoIndexOnStartup(): boolean {
    return getConfig<boolean>(CONFIG.AUTO_INDEX_ON_STARTUP, DEFAULT_CONFIG.AUTO_INDEX_ON_STARTUP);
}

/**
 * Get the index exclude patterns
 */
export function getIndexExcludePatterns(): string[] {
    return getConfig<string[]>(CONFIG.INDEX_EXCLUDE_PATTERNS, DEFAULT_CONFIG.INDEX_EXCLUDE_PATTERNS);
}

/**
 * Get the index max file size
 */
export function getIndexMaxFileSize(): number {
    return getConfig<number>(CONFIG.INDEX_MAX_FILE_SIZE, DEFAULT_CONFIG.INDEX_MAX_FILE_SIZE);
}

/**
 * Get whether web access is enabled
 */
export function getWebAccessEnabled(): boolean {
    return getConfig<boolean>(CONFIG.WEB_ACCESS_ENABLED, DEFAULT_CONFIG.WEB_ACCESS_ENABLED);
}

/**
 * Get the web access trusted sources
 */
export function getWebAccessTrustedSources(): string[] {
    return getConfig<string[]>(CONFIG.WEB_ACCESS_TRUSTED_SOURCES, DEFAULT_CONFIG.WEB_ACCESS_TRUSTED_SOURCES);
}

/**
 * Get the web access timeout
 */
export function getWebAccessTimeout(): number {
    return getConfig<number>(CONFIG.WEB_ACCESS_TIMEOUT, DEFAULT_CONFIG.WEB_ACCESS_TIMEOUT);
}

/**
 * Get the UI theme
 */
export function getUiTheme(): string {
    return getConfig<string>(CONFIG.UI_THEME, DEFAULT_CONFIG.UI_THEME);
}

/**
 * Get the UI font size
 */
export function getUiFontSize(): number {
    return getConfig<number>(CONFIG.UI_FONT_SIZE, DEFAULT_CONFIG.UI_FONT_SIZE);
}

/**
 * Get whether to show the status bar
 */
export function getUiShowStatusBar(): boolean {
    return getConfig<boolean>(CONFIG.UI_SHOW_STATUS_BAR, DEFAULT_CONFIG.UI_SHOW_STATUS_BAR);
}

/**
 * Get whether to auto-execute agent actions
 */
export function getAgentAutoExecute(): boolean {
    return getConfig<boolean>(CONFIG.AGENT_AUTO_EXECUTE, DEFAULT_CONFIG.AGENT_AUTO_EXECUTE);
}

/**
 * Get whether agent confirmation is required
 */
export function getAgentConfirmationRequired(): boolean {
    return getConfig<boolean>(CONFIG.AGENT_CONFIRMATION_REQUIRED, DEFAULT_CONFIG.AGENT_CONFIRMATION_REQUIRED);
}

/**
 * Get the terminal shell
 */
export function getTerminalShell(): string {
    return getConfig<string>(CONFIG.TERMINAL_SHELL, DEFAULT_CONFIG.TERMINAL_SHELL);
}

/**
 * Get whether to show the terminal on command
 */
export function getTerminalShowOnCommand(): boolean {
    return getConfig<boolean>(CONFIG.TERMINAL_SHOW_ON_COMMAND, DEFAULT_CONFIG.TERMINAL_SHOW_ON_COMMAND);
}
