# Model files
models/README.md
models/codegen-350M-mono/added_tokens.json
models/codegen-350M-mono/config.json
models/codegen-350M-mono/generation_config.json
models/codegen-350M-mono/merges.txt
models/codegen-350M-mono/model.safetensors
models/codegen-350M-mono/special_tokens_map.json
models/codegen-350M-mono/tokenizer_config.json
models/codegen-350M-mono/tokenizer.json
models/codegen-350M-mono/vocab.json

# Build output
out
dist

# Dependencies
node_modules
.vscode-test/

# Package files
*.vsix

# OS files
.DS_Store
