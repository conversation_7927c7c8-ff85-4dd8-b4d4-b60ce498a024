/**
 * Storage service for managing persistent data
 */

import * as vscode from 'vscode';
import { StorageItem, StorageQuery } from '../../types';
import * as logger from '../../common/logger';

/**
 * Storage service for managing persistent data
 */
export class StorageService {
    private context: vscode.ExtensionContext;
    
    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        logger.info('StorageService initialized');
    }
    
    /**
     * Store an item
     * @param key The storage key
     * @param item The item to store
     */
    public async store<T>(key: string, item: StorageItem<T>): Promise<void> {
        try {
            // Get existing items
            const items = await this.getAll<T>(key);
            
            // Find the item index
            const index = items.findIndex(i => i.id === item.id);
            
            // Update or add the item
            if (index !== -1) {
                items[index] = item;
            } else {
                items.push(item);
            }
            
            // Save the items
            await this.context.globalState.update(key, JSON.stringify(items));
            
            logger.debug(`Stored item with ID ${item.id} in ${key}`);
        } catch (error) {
            logger.error(`Failed to store item in ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Get an item
     * @param key The storage key
     * @param id The item ID
     */
    public async get<T>(key: string, id: string): Promise<StorageItem<T> | undefined> {
        try {
            // Get all items
            const items = await this.getAll<T>(key);
            
            // Find the item
            return items.find(item => item.id === id);
        } catch (error) {
            logger.error(`Failed to get item ${id} from ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Get all items
     * @param key The storage key
     */
    public async getAll<T>(key: string): Promise<StorageItem<T>[]> {
        try {
            // Get the items JSON
            const itemsJson = this.context.globalState.get<string>(key);
            
            // Parse the items
            if (itemsJson) {
                return JSON.parse(itemsJson);
            }
            
            return [];
        } catch (error) {
            logger.error(`Failed to get all items from ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Query items
     * @param key The storage key
     * @param query The query
     */
    public async query<T>(key: string, query: StorageQuery): Promise<StorageItem<T>[]> {
        try {
            // Get all items
            let items = await this.getAll<T>(key);
            
            // Filter by type
            if (query.type) {
                items = items.filter(item => item.type === query.type);
            }
            
            // Apply custom filter
            if (query.filter) {
                items = items.filter(query.filter);
            }
            
            // Apply custom sort
            if (query.sort) {
                items.sort(query.sort);
            }
            
            // Apply limit
            if (query.limit && query.limit > 0 && items.length > query.limit) {
                items = items.slice(0, query.limit);
            }
            
            return items;
        } catch (error) {
            logger.error(`Failed to query items from ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Delete an item
     * @param key The storage key
     * @param id The item ID
     */
    public async delete(key: string, id: string): Promise<void> {
        try {
            // Get all items
            const items = await this.getAll<any>(key);
            
            // Find the item index
            const index = items.findIndex(item => item.id === id);
            
            // Remove the item if found
            if (index !== -1) {
                items.splice(index, 1);
                
                // Save the items
                await this.context.globalState.update(key, JSON.stringify(items));
                
                logger.debug(`Deleted item with ID ${id} from ${key}`);
            }
        } catch (error) {
            logger.error(`Failed to delete item ${id} from ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Delete all items
     * @param key The storage key
     */
    public async deleteAll(key: string): Promise<void> {
        try {
            // Clear the items
            await this.context.globalState.update(key, undefined);
            
            logger.debug(`Deleted all items from ${key}`);
        } catch (error) {
            logger.error(`Failed to delete all items from ${key}`, error);
            throw error;
        }
    }
    
    /**
     * Create a storage item
     * @param type The item type
     * @param data The item data
     * @param id The item ID (optional, will be generated if not provided)
     */
    public createItem<T>(type: string, data: T, id?: string): StorageItem<T> {
        const now = Date.now();
        
        return {
            id: id || this.generateId(),
            type,
            data,
            createdAt: now,
            updatedAt: now
        };
    }
    
    /**
     * Generate a unique ID
     */
    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
}
