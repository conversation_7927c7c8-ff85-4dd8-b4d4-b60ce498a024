# Configuration Guide

This guide covers all configuration options available in the AutoCode VSCode Extension.

## Accessing Settings

### VSCode Settings UI
1. Open `File` → `Preferences` → `Settings`
2. Search for "autocode"
3. Modify settings using the UI

### Settings JSON
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type "Preferences: Open Settings (JSON)"
3. Add AutoCode settings to the JSON file

### Workspace Settings
Create `.vscode/settings.json` in your project root for project-specific settings.

## Model Configuration

### Basic Model Settings

```json
{
  "autocode.model.path": "models/codegen-350M-mono",
  "autocode.model.maxTokens": 1024,
  "autocode.model.temperature": 0.7,
  "autocode.model.topP": 0.9,
  "autocode.model.repetitionPenalty": 1.1
}
```

### Model Parameters

| Setting | Default | Description |
|---------|---------|-------------|
| `path` | `"models/codegen-350M-mono"` | Path to model directory |
| `maxTokens` | `1024` | Maximum tokens to generate |
| `temperature` | `0.7` | Creativity level (0.0-2.0) |
| `topP` | `0.9` | Nucleus sampling parameter |
| `repetitionPenalty` | `1.1` | Penalty for repetitive text |

### Advanced Model Settings

```json
{
  "autocode.model.loadOnStartup": true,
  "autocode.model.enableGPU": false,
  "autocode.model.batchSize": 1,
  "autocode.model.contextWindow": 2048
}
```

## Indexing Configuration

### Basic Indexing Settings

```json
{
  "autocode.indexing.autoIndexOnStartup": true,
  "autocode.indexing.maxFileSize": 1048576,
  "autocode.indexing.excludePatterns": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/.git/**"
  ]
}
```

### Indexing Parameters

| Setting | Default | Description |
|---------|---------|-------------|
| `autoIndexOnStartup` | `true` | Auto-index workspace on startup |
| `maxFileSize` | `1048576` | Max file size to index (bytes) |
| `excludePatterns` | See above | Glob patterns to exclude |
| `includePatterns` | `["**/*"]` | Glob patterns to include |

### Performance Tuning

```json
{
  "autocode.indexing.maxConcurrentFiles": 10,
  "autocode.indexing.throttleDelay": 100,
  "autocode.indexing.enableIncrementalUpdates": true,
  "autocode.indexing.cacheEnabled": true
}
```

## Web Access Configuration

### Basic Web Settings

```json
{
  "autocode.webAccess.enabled": true,
  "autocode.webAccess.timeout": 10000,
  "autocode.webAccess.trustedSources": [
    "developer.mozilla.org",
    "docs.microsoft.com",
    "stackoverflow.com",
    "github.com"
  ]
}
```

### Security Settings

```json
{
  "autocode.webAccess.allowUntrustedSources": false,
  "autocode.webAccess.maxRequestsPerMinute": 30,
  "autocode.webAccess.enableCaching": true,
  "autocode.webAccess.cacheExpiration": 3600000
}
```

## UI Configuration

### Interface Settings

```json
{
  "autocode.ui.theme": "auto",
  "autocode.ui.fontSize": 14,
  "autocode.ui.showStatusBar": true,
  "autocode.ui.showNotifications": true
}
```

### Chat Interface

```json
{
  "autocode.ui.chat.maxMessages": 100,
  "autocode.ui.chat.showTimestamps": true,
  "autocode.ui.chat.enableMarkdown": true,
  "autocode.ui.chat.autoScroll": true
}
```

### Status Bar Customization

```json
{
  "autocode.ui.statusBar.showModelStatus": true,
  "autocode.ui.statusBar.showIndexStatus": true,
  "autocode.ui.statusBar.showAgentStatus": true,
  "autocode.ui.statusBar.position": "left"
}
```

## Agent Configuration

### Agent Mode Settings

```json
{
  "autocode.agent.confirmationRequired": true,
  "autocode.agent.autoExecute": false,
  "autocode.agent.maxActionsPerSession": 50,
  "autocode.agent.enableFileOperations": true
}
```

### Agent Auto Mode Settings

```json
{
  "autocode.agentAuto.enabled": true,
  "autocode.agentAuto.maxExecutionTime": 300000,
  "autocode.agentAuto.enableTerminalCommands": true,
  "autocode.agentAuto.safetyChecks": true
}
```

## Terminal Configuration

### Basic Terminal Settings

```json
{
  "autocode.terminal.shell": "auto",
  "autocode.terminal.showOnCommand": true,
  "autocode.terminal.timeout": 30000,
  "autocode.terminal.maxOutputLength": 10000
}
```

### Security Settings

```json
{
  "autocode.terminal.allowedCommands": [
    "npm", "yarn", "git", "node", "python", "pip"
  ],
  "autocode.terminal.blockedCommands": [
    "rm -rf", "del", "format", "shutdown"
  ],
  "autocode.terminal.requireConfirmation": [
    "npm install", "git push", "rm"
  ]
}
```

## Context Management

### Context Settings

```json
{
  "autocode.context.maxItems": 20,
  "autocode.context.autoAddRelevantFiles": true,
  "autocode.context.persistAcrossSessions": true,
  "autocode.context.maxFileSize": 100000
}
```

### Context Prioritization

```json
{
  "autocode.context.priorityOrder": [
    "selection",
    "currentFile",
    "openFiles",
    "recentFiles",
    "projectFiles"
  ]
}
```

## Keyboard Shortcuts

### Default Shortcuts

```json
{
  "key": "ctrl+shift+c",
  "command": "autocode.startChat"
},
{
  "key": "ctrl+shift+a",
  "command": "autocode.startAgent"
},
{
  "key": "ctrl+shift+x",
  "command": "autocode.startAgentAuto"
}
```

### Custom Shortcuts

Add to `keybindings.json`:

```json
[
  {
    "key": "ctrl+alt+i",
    "command": "autocode.indexWorkspace"
  },
  {
    "key": "ctrl+alt+c",
    "command": "autocode.addSelectionToContext"
  }
]
```

## Performance Configuration

### Memory Management

```json
{
  "autocode.performance.maxMemoryUsage": 2048,
  "autocode.performance.enableGarbageCollection": true,
  "autocode.performance.cacheSize": 100
}
```

### Processing Limits

```json
{
  "autocode.performance.maxConcurrentRequests": 3,
  "autocode.performance.requestTimeout": 30000,
  "autocode.performance.enableThrottling": true
}
```

## Logging and Debugging

### Logging Configuration

```json
{
  "autocode.logging.level": "info",
  "autocode.logging.enableFileLogging": false,
  "autocode.logging.maxLogFileSize": 10485760,
  "autocode.logging.enableConsoleLogging": true
}
```

### Debug Settings

```json
{
  "autocode.debug.enableVerboseLogging": false,
  "autocode.debug.showInternalErrors": false,
  "autocode.debug.enablePerformanceMetrics": false
}
```

## Workspace-Specific Configuration

### Project Templates

Create `.vscode/autocode.json` for project-specific settings:

```json
{
  "projectType": "react",
  "indexing": {
    "excludePatterns": ["**/coverage/**", "**/cypress/**"]
  },
  "context": {
    "autoAddPatterns": ["src/**/*.tsx", "src/**/*.ts"]
  },
  "agent": {
    "preferredPatterns": {
      "components": "src/components",
      "hooks": "src/hooks",
      "utils": "src/utils"
    }
  }
}
```

## Configuration Profiles

### Development Profile

```json
{
  "autocode.model.temperature": 0.3,
  "autocode.agent.confirmationRequired": true,
  "autocode.logging.level": "debug",
  "autocode.indexing.autoIndexOnStartup": true
}
```

### Production Profile

```json
{
  "autocode.model.temperature": 0.7,
  "autocode.agent.confirmationRequired": false,
  "autocode.logging.level": "warn",
  "autocode.performance.enableThrottling": true
}
```

## Migration and Backup

### Export Settings

Use Command Palette: "AutoCode: Export Configuration"

### Import Settings

Use Command Palette: "AutoCode: Import Configuration"

### Reset to Defaults

Use Command Palette: "AutoCode: Reset Configuration"

## Troubleshooting Configuration

### Common Issues

1. **Settings not taking effect**
   - Restart VSCode after major changes
   - Check for syntax errors in JSON

2. **Performance issues**
   - Reduce `maxTokens` and `temperature`
   - Increase `excludePatterns` for indexing

3. **Model not loading**
   - Verify `model.path` setting
   - Check file permissions

### Validation

Use Command Palette: "AutoCode: Validate Configuration" to check for issues.

## Best Practices

1. **Start with defaults** and adjust gradually
2. **Use workspace settings** for project-specific configurations
3. **Monitor performance** and adjust limits accordingly
4. **Backup configurations** before major changes
5. **Test changes** in development environment first
