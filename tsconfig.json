{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM"], "sourceMap": true, "rootDir": "src", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": false, "noUnusedLocals": false, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "useUnknownInCatchVariables": false, "forceConsistentCasingInFileNames": true, "declaration": false, "declarationMap": false}, "exclude": ["node_modules", ".vscode-test", "out", "dist"]}