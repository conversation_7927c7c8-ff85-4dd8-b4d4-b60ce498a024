import * as vscode from 'vscode';
import { ModelService } from './services/modelService';
import { IndexService } from './services/indexService';
import { StorageService } from './services/storageService';
import { TerminalService } from './services/terminalService';
import { ContextService } from './services/contextService';
import { McpService } from './services/mcpService';
import { ChatMode } from './modes/chatMode';
import { AgentMode } from './modes/agentMode';
import { AgentAutoMode } from './modes/agentAutoMode';
import * as logger from './common/logger';
import * as constants from './common/constants';

/**
 * AutoCode VSCode Extension
 *
 * This extension provides AI-powered coding assistance using the local CodeGen-350M-Mono model.
 * It offers three operational modes:
 * - Chat: For asking questions and getting code explanations
 * - Agent: For performing actions with user confirmation
 * - Agent Auto: For autonomous operation without user intervention
 */

// Services
let modelService: ModelService;
let indexService: IndexService;
let storageService: StorageService;
let terminalService: TerminalService;
let contextService: ContextService;
let mcpService: McpService;

// Modes
let chatMode: ChatMode;
let agentMode: AgentMode;
let agentAutoMode: AgentAutoMode;

// Extension activation
export async function activate(context: vscode.ExtensionContext) {
    try {
        // Initialize services
        await initializeServices(context);

        // Register commands
        registerCommands(context);

        // Set up UI components
        setupUI(context);

        // Show welcome message
        vscode.window.showInformationMessage(constants.MESSAGES.EXTENSION_ACTIVATED);
        logger.info('AutoCode extension is now active');
    } catch (error: any) {
        vscode.window.showErrorMessage(`Failed to activate AutoCode extension: ${error.message}`);
        logger.error('Failed to activate AutoCode extension', error);
    }
}

// Register all extension commands
function registerCommands(context: vscode.ExtensionContext) {
    // Register commands using utility function
    const registerCommand = (command: string, callback: (...args: any[]) => any) => {
        const disposable = vscode.commands.registerCommand(command, callback);
        context.subscriptions.push(disposable);
        return disposable;
    };

    // Index codebase command
    registerCommand(constants.COMMANDS.INDEX_WORKSPACE, async () => {
        try {
            vscode.window.showInformationMessage('Indexing codebase...');
            const result = await indexService.indexWorkspace();
            vscode.window.showInformationMessage(
                `Indexed ${result.indexedFiles} files in ${result.elapsedTime / 1000} seconds`
            );
            logger.info(`Indexed ${result.indexedFiles} files`);
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to index codebase: ${error.message}`);
            logger.error('Failed to index codebase', error);
        }
    });

    // Add context command
    registerCommand(constants.COMMANDS.ADD_CONTEXT, async () => {
        try {
            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showInformationMessage(constants.MESSAGES.NO_EDITOR);
                return;
            }

            // Add the file as context
            const filePath = editor.document.fileName;
            await contextService.addFile(filePath);

            vscode.window.showInformationMessage(
                constants.MESSAGES.CONTEXT_ADDED.replace('{0}', filePath)
            );
            logger.info(`Added file to context: ${filePath}`);
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to add context: ${error.message}`);
            logger.error('Failed to add context', error);
        }
    });

    // Add selection to context command
    registerCommand('autocode.addSelectionToContext', async () => {
        try {
            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showInformationMessage(constants.MESSAGES.NO_EDITOR);
                return;
            }

            // Get the selection
            const selection = editor.selection;
            if (selection.isEmpty) {
                vscode.window.showInformationMessage(constants.MESSAGES.NO_SELECTION);
                return;
            }

            // Add the selection to context
            await contextService.addSelection();

            vscode.window.showInformationMessage(
                constants.MESSAGES.CONTEXT_ADDED.replace('{0}', 'selection')
            );
            logger.info('Added selection to context');
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to add selection to context: ${error.message}`);
            logger.error('Failed to add selection to context', error);
        }
    });

    // Clear context command
    registerCommand(constants.COMMANDS.CLEAR_CONTEXT, async () => {
        try {
            await contextService.clearContext();

            vscode.window.showInformationMessage(constants.MESSAGES.CONTEXT_CLEARED);
            logger.info('Cleared context');
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to clear context: ${error.message}`);
            logger.error('Failed to clear context', error);
        }
    });

    // Optimize prompt command
    registerCommand(constants.COMMANDS.OPTIMIZE_PROMPT, async () => {
        try {
            // Get the selected text
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showInformationMessage(constants.MESSAGES.NO_EDITOR);
                return;
            }

            const selection = editor.selection;
            if (selection.isEmpty) {
                vscode.window.showInformationMessage(constants.MESSAGES.NO_SELECTION);
                return;
            }

            const prompt = editor.document.getText(selection);

            // Optimize the prompt
            const optimizedPrompt = await mcpService.optimizePrompt(prompt);

            // Replace the selection with the optimized prompt
            editor.edit((editBuilder: vscode.TextEditorEdit) => {
                editBuilder.replace(selection, optimizedPrompt);
            });

            vscode.window.showInformationMessage('Prompt optimized');
            logger.info('Optimized prompt');
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to optimize prompt: ${error.message}`);
            logger.error('Failed to optimize prompt', error);
        }
    });

    // Register mode commands
    // These are already registered in their respective mode classes
}

// Initialize all services
async function initializeServices(context: vscode.ExtensionContext) {
    // Initialize logger
    logger.initialize(context);
    logger.info('Initializing services...');

    // Initialize storage service
    storageService = new StorageService(context);
    context.subscriptions.push({ dispose: () => { } }); // Storage service doesn't have a dispose method

    // Initialize model service
    modelService = new ModelService(context);
    context.subscriptions.push(modelService);
    await modelService.initialize();

    // Initialize indexing service
    indexService = new IndexService(context);
    context.subscriptions.push(indexService);
    await indexService.initialize();

    // Initialize terminal service
    terminalService = new TerminalService(context);
    context.subscriptions.push(terminalService);

    // Initialize context service
    contextService = new ContextService(context, storageService);
    context.subscriptions.push(contextService);

    // Initialize MCP service
    mcpService = new McpService(context, modelService, indexService, contextService);
    context.subscriptions.push(mcpService);

    // Initialize chat mode
    chatMode = new ChatMode(context, modelService, indexService);
    context.subscriptions.push(chatMode);

    // Initialize agent mode
    agentMode = new AgentMode(context, modelService, indexService);
    context.subscriptions.push(agentMode);

    // Initialize agent auto mode
    agentAutoMode = new AgentAutoMode(
        context,
        modelService,
        indexService,
        contextService,
        terminalService,
        mcpService
    );
    context.subscriptions.push(agentAutoMode);

    logger.info('All services initialized');
}

// Set up UI components
function setupUI(context: vscode.ExtensionContext) {
    // Create status bar items

    // Main status bar item
    const mainStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    mainStatusBarItem.text = '$(comment) AutoCode';
    mainStatusBarItem.tooltip = 'AutoCode AI Assistant';
    mainStatusBarItem.command = 'autocode.startChat';
    mainStatusBarItem.show();
    context.subscriptions.push(mainStatusBarItem);

    // Model status bar item
    const modelStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
    modelStatusBarItem.text = constants.STATUS_BAR.MODEL_LOADING;
    modelStatusBarItem.tooltip = 'Model Status';
    modelStatusBarItem.command = 'autocode.showModelStatus';
    modelStatusBarItem.show();
    context.subscriptions.push(modelStatusBarItem);

    // Index status bar item
    const indexStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 98);
    indexStatusBarItem.text = constants.STATUS_BAR.INDEX_NOT_INDEXED;
    indexStatusBarItem.tooltip = 'Index Status';
    indexStatusBarItem.command = 'autocode.showIndexStatus';
    indexStatusBarItem.show();
    context.subscriptions.push(indexStatusBarItem);

    // Agent status bar item
    const agentStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 97);
    agentStatusBarItem.text = constants.STATUS_BAR.AGENT_IDLE;
    agentStatusBarItem.tooltip = 'Agent Status';
    agentStatusBarItem.command = 'autocode.startAgent';
    agentStatusBarItem.show();
    context.subscriptions.push(agentStatusBarItem);

    // Agent Auto status bar item
    const agentAutoStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 96);
    agentAutoStatusBarItem.text = constants.STATUS_BAR.AGENT_AUTO_IDLE;
    agentAutoStatusBarItem.tooltip = 'Agent Auto Status';
    agentAutoStatusBarItem.command = 'autocode.startAgentAuto';
    agentAutoStatusBarItem.show();
    context.subscriptions.push(agentAutoStatusBarItem);

    // Register webview views
    // These will be implemented in future updates

    logger.debug('UI components set up');
}

// Extension deactivation
export function deactivate() {
    logger.info('AutoCode extension is now deactivated');

    // Clean up resources
    if (agentAutoMode) {
        agentAutoMode.dispose();
    }

    if (agentMode) {
        agentMode.dispose();
    }

    if (chatMode) {
        chatMode.dispose();
    }

    if (mcpService) {
        mcpService.dispose();
    }

    if (contextService) {
        contextService.dispose();
    }

    if (terminalService) {
        terminalService.dispose();
    }

    if (indexService) {
        indexService.dispose();
    }

    if (modelService) {
        modelService.dispose();
    }

    logger.info('All resources cleaned up');
}
