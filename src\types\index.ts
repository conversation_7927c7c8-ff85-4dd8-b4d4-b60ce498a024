/**
 * Common types used throughout the extension
 */

import * as vscode from 'vscode';

/**
 * Model types
 */

// Model configuration
export interface ModelConfig {
    modelPath: string;
    maxTokens: number;
    temperature: number;
    topP: number;
    repetitionPenalty: number;
}

// Model request
export interface ModelRequest {
    prompt: string;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    repetitionPenalty?: number;
}

// Model response
export interface ModelResponse {
    text: string;
    tokens: number;
    processingTime: number;
}

/**
 * Indexer types
 */

// Index entry
export interface IndexEntry {
    uri: vscode.Uri;
    fileName: string;
    filePath: string;
    fileExtension: string;
    content: string;
    symbols: CodeSymbol[];
    lastModified: number;
}

// Symbol
export interface CodeSymbol {
    name: string;
    kind: vscode.SymbolKind;
    range: vscode.Range;
    detail?: string;
    containerName?: string;
}

// Index query
export interface IndexQuery {
    text?: string;
    filePattern?: string;
    symbolKind?: vscode.SymbolKind;
    maxResults?: number;
}

// Index result
export interface IndexResult {
    entries: IndexEntry[];
    totalFiles: number;
    indexedFiles: number;
    skippedFiles: number;
    elapsedTime: number;
}

// Query result
export interface QueryResult {
    entries: IndexEntry[];
    symbols: CodeSymbol[];
    totalResults: number;
    elapsedTime: number;
}

/**
 * Web access types
 */

// Search result
export interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    source: string;
}

// Documentation
export interface Documentation {
    title: string;
    content: string;
    url: string;
    source: string;
}

// Code example
export interface CodeExample {
    title: string;
    code: string;
    language: string;
    source: string;
    url: string;
}

// Package info
export interface PackageInfo {
    name: string;
    version: string;
    description: string;
    author: string;
    license: string;
    dependencies: Record<string, string>;
    repository?: string;
}

/**
 * Chat mode types
 */

// Chat message
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
}

// Chat session
export interface ChatSession {
    id: string;
    name: string;
    messages: ChatMessage[];
    createdAt: number;
    updatedAt: number;
}

/**
 * Agent mode types
 */

// Agent action
export interface AgentAction {
    id: string;
    type: 'file_create' | 'file_update' | 'file_delete' | 'terminal_command';
    description: string;
    details: any;
    status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
    result?: string;
    error?: string;
    timestamp: number;
}

// Agent session
export interface AgentSession {
    id: string;
    name: string;
    task: string;
    actions: AgentAction[];
    createdAt: number;
    updatedAt: number;
    status: 'in_progress' | 'completed' | 'failed';
}

/**
 * Agent Auto mode types
 */

// Agent Auto action
export interface AgentAutoAction extends AgentAction {
    // Additional properties specific to auto mode
    priority: number;
    dependencies: string[]; // IDs of actions that must be completed before this one
}

// Agent Auto session
export interface AgentAutoSession {
    id: string;
    name: string;
    task: string;
    actions: AgentAutoAction[];
    createdAt: number;
    updatedAt: number;
    status: 'in_progress' | 'completed' | 'failed';
    autoExecute: boolean;
}

/**
 * Context types
 */

// Context item
export interface ContextItem {
    id: string;
    type: 'file' | 'folder' | 'selection' | 'terminal' | 'web';
    name: string;
    content: string;
    uri?: vscode.Uri;
    createdAt: number;
}

// Context
export interface Context {
    id: string;
    name: string;
    items: ContextItem[];
    createdAt: number;
    updatedAt: number;
}

/**
 * Terminal types
 */

// Terminal command
export interface TerminalCommand {
    id: string;
    command: string;
    cwd?: string;
    env?: Record<string, string>;
    timestamp: number;
}

// Terminal output
export interface TerminalOutput {
    commandId: string;
    output: string;
    exitCode?: number;
    timestamp: number;
}

/**
 * MCP types
 */

// MCP source
export interface McpSource {
    id: string;
    type: 'file' | 'folder' | 'git' | 'web' | 'database';
    name: string;
    uri?: vscode.Uri;
    config?: any;
}

// MCP query
export interface McpQuery {
    text: string;
    sources?: string[]; // Source IDs
    maxResults?: number;
}

// MCP result
export interface McpResult {
    query: string;
    results: McpResultItem[];
    totalResults: number;
    elapsedTime: number;
}

// MCP result item
export interface McpResultItem {
    sourceId: string;
    content: string;
    relevance: number;
    metadata?: any;
}

/**
 * Storage types
 */

// Storage item
export interface StorageItem<T> {
    id: string;
    type: string;
    data: T;
    createdAt: number;
    updatedAt: number;
}

// Storage query
export interface StorageQuery {
    type?: string;
    filter?: (item: StorageItem<any>) => boolean;
    sort?: (a: StorageItem<any>, b: StorageItem<any>) => number;
    limit?: number;
}

/**
 * UI types
 */

// View type
export type ViewType = 'chat' | 'agent' | 'agentAuto' | 'context' | 'settings';

// View state
export interface ViewState {
    type: ViewType;
    data?: any;
}

// Message from webview
export interface WebviewMessage {
    command: string;
    data?: any;
}

// Message to webview
export interface HostMessage {
    command: string;
    data?: any;
}
