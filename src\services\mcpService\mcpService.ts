/**
 * MCP (Model, Context, Prompt) service for managing model interactions with context
 */

import * as vscode from 'vscode';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, McpResultItem, McpSource } from '../../types';
import { ModelService } from '../modelService';
import { IndexService } from '../indexService';
import { ContextService } from '../contextService';
import { WebAccess } from '../../api/webAccess';
import * as logger from '../../common/logger';

/**
 * MCP (Model, Context, Prompt) service for managing model interactions with context
 */
export class McpService {
    private context: vscode.ExtensionContext;
    private modelService: ModelService;
    private indexService: IndexService;
    private contextService: ContextService;
    private webAccess: WebAccess;
    private sources: Map<string, McpSource> = new Map();
    private disposables: vscode.Disposable[] = [];
    
    constructor(
        context: vscode.ExtensionContext,
        modelService: ModelService,
        indexService: IndexService,
        contextService: ContextService
    ) {
        this.context = context;
        this.modelService = modelService;
        this.indexService = indexService;
        this.contextService = contextService;
        this.webAccess = new WebAccess();
        
        // Register sources
        this.registerDefaultSources();
        
        logger.info('McpService initialized');
    }
    
    /**
     * Register default sources
     */
    private registerDefaultSources(): void {
        // Register workspace source
        this.registerSource({
            id: 'workspace',
            type: 'folder',
            name: 'Workspace'
        });
        
        // Register context source
        this.registerSource({
            id: 'context',
            type: 'file',
            name: 'Context'
        });
        
        // Register web source
        this.registerSource({
            id: 'web',
            type: 'web',
            name: 'Web'
        });
    }
    
    /**
     * Register a source
     * @param source The source
     */
    public registerSource(source: McpSource): void {
        this.sources.set(source.id, source);
        logger.debug(`Registered source: ${source.name} (${source.id})`);
    }
    
    /**
     * Unregister a source
     * @param sourceId The source ID
     */
    public unregisterSource(sourceId: string): void {
        this.sources.delete(sourceId);
        logger.debug(`Unregistered source: ${sourceId}`);
    }
    
    /**
     * Get a source
     * @param sourceId The source ID
     */
    public getSource(sourceId: string): McpSource | undefined {
        return this.sources.get(sourceId);
    }
    
    /**
     * Get all sources
     */
    public getAllSources(): McpSource[] {
        return Array.from(this.sources.values());
    }
    
    /**
     * Query sources
     * @param query The query
     */
    public async query(query: McpQuery): Promise<McpResult> {
        try {
            const startTime = Date.now();
            
            // Determine which sources to query
            const sourcesToQuery = query.sources
                ? query.sources.map(id => this.sources.get(id)).filter(Boolean) as McpSource[]
                : Array.from(this.sources.values());
            
            if (sourcesToQuery.length === 0) {
                throw new Error('No sources to query');
            }
            
            // Query each source
            const results: McpResultItem[] = [];
            
            for (const source of sourcesToQuery) {
                try {
                    const sourceResults = await this.querySource(source, query);
                    results.push(...sourceResults);
                } catch (error) {
                    logger.warn(`Failed to query source ${source.id}`, error);
                }
            }
            
            // Sort results by relevance
            results.sort((a, b) => b.relevance - a.relevance);
            
            // Limit results if needed
            const limitedResults = query.maxResults && query.maxResults > 0
                ? results.slice(0, query.maxResults)
                : results;
            
            // Create the result
            const result: McpResult = {
                query: query.text,
                results: limitedResults,
                totalResults: results.length,
                elapsedTime: Date.now() - startTime
            };
            
            logger.debug(`Query completed: ${query.text} (${result.totalResults} results)`);
            
            return result;
        } catch (error) {
            logger.error(`Failed to query: ${query.text}`, error);
            throw error;
        }
    }
    
    /**
     * Query a source
     * @param source The source
     * @param query The query
     */
    private async querySource(source: McpSource, query: McpQuery): Promise<McpResultItem[]> {
        switch (source.type) {
            case 'folder':
                return this.queryWorkspace(query);
            case 'file':
                return this.queryContext(query);
            case 'web':
                return this.queryWeb(query);
            default:
                return [];
        }
    }
    
    /**
     * Query the workspace
     * @param query The query
     */
    private async queryWorkspace(query: McpQuery): Promise<McpResultItem[]> {
        // Query the index
        const indexResult = await this.indexService.query({
            text: query.text
        });
        
        // Convert to MCP result items
        return indexResult.entries.map(entry => ({
            sourceId: 'workspace',
            content: entry.content,
            relevance: 1.0, // TODO: Calculate relevance
            metadata: {
                fileName: entry.fileName,
                filePath: entry.filePath,
                symbols: entry.symbols
            }
        }));
    }
    
    /**
     * Query the context
     * @param query The query
     */
    private async queryContext(query: McpQuery): Promise<McpResultItem[]> {
        // Get the current context
        const context = this.contextService.getCurrentContext();
        
        if (!context) {
            return [];
        }
        
        // Filter items by relevance to the query
        // This is a simple implementation that checks if the query text appears in the content
        const results: McpResultItem[] = [];
        
        for (const item of context.items) {
            // Check if the query text appears in the content
            if (item.content.toLowerCase().includes(query.text.toLowerCase())) {
                results.push({
                    sourceId: 'context',
                    content: item.content,
                    relevance: 1.0, // TODO: Calculate relevance
                    metadata: {
                        name: item.name,
                        type: item.type,
                        uri: item.uri
                    }
                });
            }
        }
        
        return results;
    }
    
    /**
     * Query the web
     * @param query The query
     */
    private async queryWeb(query: McpQuery): Promise<McpResultItem[]> {
        // Search the web
        const searchResults = await this.webAccess.searchInformation(query.text);
        
        // Convert to MCP result items
        return searchResults.map(result => ({
            sourceId: 'web',
            content: result.snippet,
            relevance: 0.8, // Web results are less relevant than local ones
            metadata: {
                title: result.title,
                url: result.url,
                source: result.source
            }
        }));
    }
    
    /**
     * Generate a response
     * @param query The query
     * @param context The context
     */
    public async generateResponse(query: string, context?: string): Promise<string> {
        try {
            // Prepare the prompt
            let prompt = '';
            
            // Add context if provided
            if (context) {
                prompt += `Context:\n${context}\n\n`;
            }
            
            // Add query
            prompt += `Query: ${query}\n\n`;
            prompt += 'Response:';
            
            // Generate text
            const response = await this.modelService.generateText({
                prompt,
                maxTokens: 1024,
                temperature: 0.7
            });
            
            logger.debug(`Generated response for query: ${query}`);
            
            return response.text;
        } catch (error) {
            logger.error(`Failed to generate response for query: ${query}`, error);
            throw error;
        }
    }
    
    /**
     * Optimize a prompt
     * @param prompt The prompt to optimize
     */
    public async optimizePrompt(prompt: string): Promise<string> {
        try {
            // Prepare the optimization prompt
            const optimizationPrompt = `
                I need to optimize the following prompt for better results with the CodeGen-350M-Mono model.
                
                Original prompt:
                ${prompt}
                
                Please provide an optimized version of this prompt that:
                1. Is clear and specific
                2. Provides necessary context
                3. Uses appropriate formatting
                4. Avoids ambiguity
                
                Optimized prompt:
            `;
            
            // Generate text
            const response = await this.modelService.generateText({
                prompt: optimizationPrompt,
                maxTokens: 1024,
                temperature: 0.7
            });
            
            logger.debug(`Optimized prompt`);
            
            return response.text;
        } catch (error) {
            logger.error(`Failed to optimize prompt`, error);
            throw error;
        }
    }
    
    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.disposables = [];
    }
}
