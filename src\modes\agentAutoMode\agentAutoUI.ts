/**
 * Agent Auto UI
 */

import * as vscode from 'vscode';
import { AgentAutoSession } from '../../types';
import * as logger from '../../common/logger';
import { WEBVIEW } from '../../common/constants';

/**
 * Agent Auto UI
 */
export class AgentAutoUI {
    private context: vscode.ExtensionContext;
    private webviewPanel: vscode.WebviewPanel | null = null;
    private disposables: vscode.Disposable[] = [];
    private messageHandler: (message: any) => void;
    
    constructor(
        context: vscode.ExtensionContext,
        messageHandler: (message: any) => void
    ) {
        this.context = context;
        this.messageHandler = messageHandler;
    }
    
    /**
     * Show the UI
     * @param session The session
     */
    public showUI(session: AgentAutoSession): void {
        // Check if panel already exists
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            this.updateSession(session);
            return;
        }
        
        // Create webview panel
        this.webviewPanel = vscode.window.createWebviewPanel(
            WEBVIEW.AGENT_AUTO_VIEW_TYPE,
            'AutoCode Agent Auto',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );
        
        // Set webview content
        this.updateSession(session);
        
        // Handle messages from webview
        this.webviewPanel.webview.onDidReceiveMessage(
            this.messageHandler,
            undefined,
            this.disposables
        );
        
        // Handle panel disposal
        this.webviewPanel.onDidDispose(
            () => {
                this.webviewPanel = null;
            },
            null,
            this.disposables
        );
        
        logger.debug('Showed agent auto UI');
    }
    
    /**
     * Update the session
     * @param session The session
     */
    public updateSession(session: AgentAutoSession): void {
        if (!this.webviewPanel) {
            return;
        }
        
        // Create HTML content
        const html = this.getHtml(session);
        
        // Set webview content
        this.webviewPanel.webview.html = html;
        
        logger.debug('Updated agent auto UI');
    }
    
    /**
     * Update the status
     * @param status The status
     */
    public updateStatus(status: string): void {
        if (!this.webviewPanel) {
            return;
        }
        
        // Send message to webview
        this.webviewPanel.webview.postMessage({
            command: 'updateStatus',
            status
        });
        
        logger.debug(`Updated agent auto UI status: ${status}`);
    }
    
    /**
     * Clear the UI
     */
    public clearUI(): void {
        if (!this.webviewPanel) {
            return;
        }
        
        // Create HTML content
        const html = this.getHtml(null);
        
        // Set webview content
        this.webviewPanel.webview.html = html;
        
        logger.debug('Cleared agent auto UI');
    }
    
    /**
     * Get the HTML
     * @param session The session
     */
    private getHtml(session: AgentAutoSession | null): string {
        // This is a placeholder implementation
        // In a real implementation, you would create a more sophisticated UI
        
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AutoCode Agent Auto</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 0;
                        margin: 0;
                    }
                    .container {
                        display: flex;
                        flex-direction: column;
                        height: 100vh;
                        padding: 20px;
                    }
                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                    }
                    .status {
                        font-style: italic;
                    }
                    .task {
                        margin-bottom: 20px;
                    }
                    .controls {
                        display: flex;
                        gap: 10px;
                        margin-bottom: 20px;
                    }
                    .actions {
                        flex: 1;
                        overflow-y: auto;
                    }
                    .action {
                        margin-bottom: 15px;
                        padding: 10px;
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 5px;
                    }
                    .action-header {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                    }
                    .action-type {
                        font-weight: bold;
                    }
                    .action-status {
                        font-style: italic;
                    }
                    .action-description {
                        margin-bottom: 5px;
                    }
                    .action-details {
                        margin-bottom: 5px;
                        padding: 5px;
                        background-color: var(--vscode-editor-lineHighlightBackground);
                        border-radius: 3px;
                        font-family: monospace;
                        white-space: pre-wrap;
                    }
                    .action-result {
                        margin-top: 5px;
                        font-style: italic;
                    }
                    .action-error {
                        margin-top: 5px;
                        color: var(--vscode-errorForeground);
                    }
                    .action-buttons {
                        display: flex;
                        justify-content: flex-end;
                        margin-top: 5px;
                    }
                    button {
                        padding: 5px 10px;
                        margin-left: 5px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 3px;
                        cursor: pointer;
                    }
                    button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    button:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                    .pending {
                        border-left: 3px solid var(--vscode-editorInfo-foreground);
                    }
                    .approved {
                        border-left: 3px solid var(--vscode-editorWarning-foreground);
                    }
                    .completed {
                        border-left: 3px solid var(--vscode-editorGreen-foreground);
                    }
                    .rejected {
                        border-left: 3px solid var(--vscode-editorHint-foreground);
                    }
                    .failed {
                        border-left: 3px solid var(--vscode-editorError-foreground);
                    }
                    .no-session {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    ${session ? `
                        <div class="header">
                            <div class="title">${session.name}</div>
                            <div class="status">${session.status}</div>
                        </div>
                        <div class="task">
                            <h2>Task</h2>
                            <p>${session.task}</p>
                        </div>
                        <div class="controls">
                            <button id="start-button" ${session.status !== 'in_progress' ? 'disabled' : ''}>Start</button>
                            <button id="pause-button" ${session.status !== 'in_progress' ? 'disabled' : ''}>Pause</button>
                            <button id="resume-button" ${session.status !== 'in_progress' ? 'disabled' : ''}>Resume</button>
                            <button id="stop-button" ${session.status !== 'in_progress' ? 'disabled' : ''}>Stop</button>
                            <button id="regenerate-button">Regenerate Plan</button>
                            <button id="new-session-button">New Session</button>
                        </div>
                        <h2>Actions</h2>
                        <div class="actions">
                            ${session.actions.map(action => `
                                <div class="action ${action.status}">
                                    <div class="action-header">
                                        <div class="action-type">${action.type}</div>
                                        <div class="action-status">${action.status}</div>
                                    </div>
                                    <div class="action-description">${action.description}</div>
                                    <div class="action-details">
                                        ${action.type === 'file_create' || action.type === 'file_update' ? `
                                            Path: ${action.details.filePath || 'N/A'}
                                            ${action.details.fileContent ? `\nContent:\n${action.details.fileContent}` : ''}
                                        ` : action.type === 'file_delete' ? `
                                            Path: ${action.details.filePath || 'N/A'}
                                        ` : action.type === 'terminal_command' ? `
                                            Command: ${action.details.command || 'N/A'}
                                            ${action.details.cwd ? `\nWorking Directory: ${action.details.cwd}` : ''}
                                        ` : ''}
                                    </div>
                                    ${action.dependencies && action.dependencies.length > 0 ? `
                                        <div class="action-dependencies">
                                            Dependencies: ${action.dependencies.join(', ')}
                                        </div>
                                    ` : ''}
                                    ${action.result ? `<div class="action-result">${action.result}</div>` : ''}
                                    ${action.error ? `<div class="action-error">${action.error}</div>` : ''}
                                    ${action.status === 'pending' && !session.autoExecute ? `
                                        <div class="action-buttons">
                                            <button class="approve-button" data-id="${action.id}">Approve</button>
                                            <button class="reject-button" data-id="${action.id}">Reject</button>
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : `
                        <div class="no-session">
                            <h2>No session selected</h2>
                            <p>Create a new session to get started</p>
                            <button id="new-session-button">New Session</button>
                        </div>
                    `}
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    // Handle start button click
                    document.getElementById('start-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'startExecution'
                        });
                    });
                    
                    // Handle pause button click
                    document.getElementById('pause-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'pauseExecution'
                        });
                    });
                    
                    // Handle resume button click
                    document.getElementById('resume-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'resumeExecution'
                        });
                    });
                    
                    // Handle stop button click
                    document.getElementById('stop-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'stopExecution'
                        });
                    });
                    
                    // Handle regenerate button click
                    document.getElementById('regenerate-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'regeneratePlan'
                        });
                    });
                    
                    // Handle new session button click
                    document.getElementById('new-session-button')?.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'newSession'
                        });
                    });
                    
                    // Handle approve button click
                    document.querySelectorAll('.approve-button').forEach(button => {
                        button.addEventListener('click', () => {
                            vscode.postMessage({
                                command: 'approveAction',
                                actionId: button.dataset.id
                            });
                        });
                    });
                    
                    // Handle reject button click
                    document.querySelectorAll('.reject-button').forEach(button => {
                        button.addEventListener('click', () => {
                            vscode.postMessage({
                                command: 'rejectAction',
                                actionId: button.dataset.id
                            });
                        });
                    });
                    
                    // Handle messages from extension
                    window.addEventListener('message', event => {
                        const message = event.data;
                        
                        if (message.command === 'updateStatus') {
                            const statusElement = document.querySelector('.status');
                            if (statusElement) {
                                statusElement.textContent = message.status;
                            }
                        }
                    });
                </script>
            </body>
            </html>
        `;
    }
    
    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of webview panel
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
            this.webviewPanel = null;
        }
        
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.disposables = [];
    }
}
