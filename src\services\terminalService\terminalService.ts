/**
 * Terminal service for managing terminal interactions
 */

import * as vscode from 'vscode';
import { TerminalCommand, TerminalOutput } from '../../types';
import * as logger from '../../common/logger';
import * as config from '../../common/config';

/**
 * Terminal service for managing terminal interactions
 */
export class TerminalService {
    private context: vscode.ExtensionContext;
    private terminal: vscode.Terminal | null = null;
    private commands: Map<string, TerminalCommand> = new Map();
    private outputs: Map<string, TerminalOutput> = new Map();
    private disposables: vscode.Disposable[] = [];
    
    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        
        // Register terminal close event
        this.disposables.push(
            vscode.window.onDidCloseTerminal(terminal => {
                if (this.terminal === terminal) {
                    this.terminal = null;
                }
            })
        );
        
        logger.info('TerminalService initialized');
    }
    
    /**
     * Execute a command
     * @param command The command to execute
     * @param cwd The working directory
     * @param env The environment variables
     */
    public async executeCommand(command: string, cwd?: string, env?: Record<string, string>): Promise<TerminalOutput> {
        try {
            // Create a terminal command
            const terminalCommand: TerminalCommand = {
                id: this.generateId(),
                command,
                cwd,
                env,
                timestamp: Date.now()
            };
            
            // Store the command
            this.commands.set(terminalCommand.id, terminalCommand);
            
            // Create a terminal if needed
            if (!this.terminal) {
                this.createTerminal();
            }
            
            // Show the terminal if configured
            if (config.getTerminalShowOnCommand()) {
                this.terminal!.show();
            }
            
            // Execute the command
            this.terminal!.sendText(command);
            
            // Create a placeholder output
            const output: TerminalOutput = {
                commandId: terminalCommand.id,
                output: '',
                timestamp: Date.now()
            };
            
            // Store the output
            this.outputs.set(terminalCommand.id, output);
            
            // TODO: Implement a way to capture the output and exit code
            // This is a limitation of the VS Code API, which doesn't provide a way to capture terminal output
            // For now, we'll just return the placeholder output
            
            logger.debug(`Executed command: ${command}`);
            
            return output;
        } catch (error) {
            logger.error(`Failed to execute command: ${command}`, error);
            throw error;
        }
    }
    
    /**
     * Get a command
     * @param id The command ID
     */
    public getCommand(id: string): TerminalCommand | undefined {
        return this.commands.get(id);
    }
    
    /**
     * Get all commands
     */
    public getAllCommands(): TerminalCommand[] {
        return Array.from(this.commands.values());
    }
    
    /**
     * Get an output
     * @param id The command ID
     */
    public getOutput(id: string): TerminalOutput | undefined {
        return this.outputs.get(id);
    }
    
    /**
     * Get all outputs
     */
    public getAllOutputs(): TerminalOutput[] {
        return Array.from(this.outputs.values());
    }
    
    /**
     * Clear the terminal
     */
    public clearTerminal(): void {
        if (this.terminal) {
            this.terminal.sendText('clear');
        }
    }
    
    /**
     * Show the terminal
     */
    public showTerminal(): void {
        if (this.terminal) {
            this.terminal.show();
        } else {
            this.createTerminal();
            this.terminal!.show();
        }
    }
    
    /**
     * Hide the terminal
     */
    public hideTerminal(): void {
        if (this.terminal) {
            this.terminal.hide();
        }
    }
    
    /**
     * Dispose of the terminal
     */
    public disposeTerminal(): void {
        if (this.terminal) {
            this.terminal.dispose();
            this.terminal = null;
        }
    }
    
    /**
     * Create a terminal
     */
    private createTerminal(): void {
        // Get the shell
        const shell = config.getTerminalShell();
        
        // Create the terminal
        if (shell === 'default') {
            this.terminal = vscode.window.createTerminal('AutoCode');
        } else {
            this.terminal = vscode.window.createTerminal({
                name: 'AutoCode',
                shellPath: shell
            });
        }
    }
    
    /**
     * Generate a unique ID
     */
    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    
    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of the terminal
        this.disposeTerminal();
        
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.disposables = [];
    }
}
