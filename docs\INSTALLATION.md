# Installation Guide

This guide provides detailed instructions for installing and setting up the AutoCode VSCode Extension.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10, macOS 10.14, or Linux (Ubuntu 18.04+)
- **VSCode**: Version 1.60.0 or higher
- **Node.js**: Version 16.0 or higher
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 1GB free space for model files
- **Internet**: Required for initial setup and optional web access features

### Recommended Requirements
- **RAM**: 8GB or more for optimal performance
- **Storage**: 2GB free space for caching and temporary files
- **CPU**: Multi-core processor for faster indexing

## Installation Methods

### Method 1: From Release (Recommended)

1. **Download the Extension**
   - Go to the [Releases page](https://github.com/your-username/autocode-vscode-extension/releases)
   - Download the latest `.vsix` file

2. **Install in VSCode**
   - Open VSCode
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on macOS)
   - Type "Extensions: Install from VSIX"
   - Select the downloaded `.vsix` file
   - Restart VSCode when prompted

### Method 2: Building and Running Locally

The extension must be built before it can be run locally. Follow these steps:

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-username/autocode-vscode-extension.git
   cd autocode-vscode-extension
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Build the Extension**
   ```bash
   npm run build
   # or for development with TypeScript compilation
   npm run compile
   ```

4. **Run in Development Mode**
   - Press `F5` in VS Code to start debugging
   - A new VS Code window will open with the extension loaded

5. **Verify Build (Optional)**
   ```bash
   npm run verify
   ```
   - This runs a comprehensive build verification
   - Checks TypeScript compilation, webpack bundling, and file integrity

6. **Alternative: Package for Installation**
   ```bash
   npm install -g vsce
   vsce package
   ```
   - Then install the generated `.vsix` file

## Model Setup

### Automatic Model Download (Recommended)

The extension will automatically prompt you to download the required model files on first activation:

1. **First Activation**
   - Open VSCode with the extension installed
   - You'll see a notification about model setup
   - Click "Download Model" to start automatic download

2. **Manual Model Verification**
   - Check the status bar for model status
   - If showing "Model: Not Loaded", use Command Palette:
   - `Ctrl+Shift+P` → "AutoCode: Reload Model"

### Manual Model Setup

If automatic download fails, you can manually set up the model:

1. **Create Model Directory**
   ```bash
   mkdir -p models/codegen-350M-mono
   ```

2. **Download Model Files**
   - Download from [Hugging Face](https://huggingface.co/Salesforce/codegen-350M-mono)
   - Required files:
     - `model.onnx` (or equivalent format)
     - `tokenizer.json`
     - `vocab.json`
     - `config.json`

3. **Place Files**
   - Copy all model files to `models/codegen-350M-mono/`
   - Ensure proper file permissions

4. **Verify Installation**
   - Restart VSCode
   - Check status bar for "Model: Ready"

## Initial Configuration

### Basic Setup

1. **Open Settings**
   - `File` → `Preferences` → `Settings`
   - Search for "autocode"

2. **Essential Settings**
   ```json
   {
     "autocode.model.path": "models/codegen-350M-mono",
     "autocode.indexing.autoIndexOnStartup": true,
     "autocode.ui.showStatusBar": true
   }
   ```

### Workspace Configuration

1. **Open a Project**
   - Open your project folder in VSCode
   - Wait for the indexing process to complete

2. **Verify Indexing**
   - Check status bar for "Index: Indexed"
   - Use Command Palette: "AutoCode: Show Index Status"

## Verification

### Test Installation

1. **Check Extension Status**
   - Look for AutoCode items in the status bar
   - All should show "Ready" or "Idle" status

2. **Test Chat Mode**
   - Press `Ctrl+Shift+P`
   - Type "AutoCode: Start Chat Mode"
   - Ask a simple question like "Hello"

3. **Test Indexing**
   - Command Palette: "AutoCode: Index Workspace"
   - Monitor progress in status bar

### Common Status Indicators

| Status | Meaning | Action |
|--------|---------|--------|
| Model: Loading | Model is being loaded | Wait for completion |
| Model: Ready | Model is ready to use | Normal operation |
| Model: Error | Model failed to load | Check model files |
| Index: Indexing | Workspace is being indexed | Wait for completion |
| Index: Indexed | Indexing complete | Normal operation |

## Troubleshooting Installation

### Model Issues

**Problem**: Model not loading
- **Solution**: Check model file paths and permissions
- **Command**: "AutoCode: Reload Model"

**Problem**: Out of memory during model loading
- **Solution**: Close other applications, increase available RAM
- **Alternative**: Adjust model settings for lower memory usage

### Extension Issues

**Problem**: Extension not activating
- **Solution**: Check VSCode version compatibility
- **Action**: Update VSCode to latest version

**Problem**: Commands not appearing
- **Solution**: Restart VSCode
- **Action**: Disable/enable extension

### Performance Issues

**Problem**: Slow indexing
- **Solution**: Exclude large directories from indexing
- **Setting**: `autocode.indexing.excludePatterns`

**Problem**: High CPU usage
- **Solution**: Adjust indexing settings
- **Setting**: Reduce `autocode.indexing.maxFileSize`

## Uninstallation

### Complete Removal

1. **Disable Extension**
   - Go to Extensions view (`Ctrl+Shift+X`)
   - Find AutoCode extension
   - Click "Disable" then "Uninstall"

2. **Remove Model Files**
   ```bash
   rm -rf models/codegen-350M-mono
   ```

3. **Clear Settings**
   - Remove AutoCode settings from VSCode settings
   - Clear workspace-specific configurations

4. **Clear Cache**
   - Remove extension data from VSCode storage
   - Location varies by OS:
     - Windows: `%APPDATA%\Code\User\globalStorage`
     - macOS: `~/Library/Application Support/Code/User/globalStorage`
     - Linux: `~/.config/Code/User/globalStorage`

## Next Steps

After successful installation:

1. **Read the [User Guide](USER_GUIDE.md)** for detailed usage instructions
2. **Configure settings** in [Configuration Guide](CONFIGURATION.md)
3. **Explore operational modes** in [Operational Modes](OPERATIONAL_MODES.md)
4. **Join the community** for support and updates

## Support

If you encounter issues during installation:

1. **Check [Troubleshooting Guide](TROUBLESHOOTING.md)**
2. **Review [GitHub Issues](https://github.com/your-username/autocode-vscode-extension/issues)**
3. **Create a new issue** with detailed error information
4. **Join our Discord** for community support
