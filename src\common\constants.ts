/**
 * Common constants used throughout the extension
 */

/**
 * Extension constants
 */
export const EXTENSION_NAME = 'autocode';
export const EXTENSION_DISPLAY_NAME = 'AutoCode';
export const EXTENSION_VERSION = '0.1.0';

/**
 * Command constants
 */
export const COMMANDS = {
    // Chat mode commands
    START_CHAT: 'autocode.startChat',
    SEND_CHAT_MESSAGE: 'autocode.sendChatMessage',
    CLEAR_CHAT: 'autocode.clearChat',
    
    // Agent mode commands
    START_AGENT: 'autocode.startAgent',
    APPROVE_ACTION: 'autocode.approveAction',
    REJECT_ACTION: 'autocode.rejectAction',
    
    // Agent Auto mode commands
    START_AGENT_AUTO: 'autocode.startAgentAuto',
    PAUSE_AGENT_AUTO: 'autocode.pauseAgentAuto',
    RESUME_AGENT_AUTO: 'autocode.resumeAgentAuto',
    STOP_AGENT_AUTO: 'autocode.stopAgentAuto',
    
    // Indexing commands
    INDEX_WORKSPACE: 'autocode.indexWorkspace',
    CLEAR_INDEX: 'autocode.clearIndex',
    
    // Context commands
    ADD_CONTEXT: 'autocode.addContext',
    REMOVE_CONTEXT: 'autocode.removeContext',
    CLEAR_CONTEXT: 'autocode.clearContext',
    
    // Model commands
    RELOAD_MODEL: 'autocode.reloadModel',
    UNLOAD_MODEL: 'autocode.unloadModel',
    SHOW_MODEL_STATUS: 'autocode.showModelStatus',
    
    // Prompt commands
    OPTIMIZE_PROMPT: 'autocode.optimizePrompt',
    
    // View commands
    SHOW_CHAT_VIEW: 'autocode.showChatView',
    SHOW_AGENT_VIEW: 'autocode.showAgentView',
    SHOW_AGENT_AUTO_VIEW: 'autocode.showAgentAutoView',
    SHOW_CONTEXT_VIEW: 'autocode.showContextView',
    SHOW_SETTINGS_VIEW: 'autocode.showSettingsView'
};

/**
 * View constants
 */
export const VIEWS = {
    CHAT: 'autocode.chatView',
    AGENT: 'autocode.agentView',
    AGENT_AUTO: 'autocode.agentAutoView',
    CONTEXT: 'autocode.contextView',
    SETTINGS: 'autocode.settingsView'
};

/**
 * Storage constants
 */
export const STORAGE_KEYS = {
    CHAT_SESSIONS: 'autocode.chatSessions',
    AGENT_SESSIONS: 'autocode.agentSessions',
    AGENT_AUTO_SESSIONS: 'autocode.agentAutoSessions',
    CONTEXTS: 'autocode.contexts',
    SETTINGS: 'autocode.settings'
};

/**
 * Configuration constants
 */
export const CONFIG = {
    // Model configuration
    MODEL_PATH: 'autocode.model.path',
    MODEL_MAX_TOKENS: 'autocode.model.maxTokens',
    MODEL_TEMPERATURE: 'autocode.model.temperature',
    MODEL_TOP_P: 'autocode.model.topP',
    MODEL_REPETITION_PENALTY: 'autocode.model.repetitionPenalty',
    
    // Indexing configuration
    AUTO_INDEX_ON_STARTUP: 'autocode.indexing.autoIndexOnStartup',
    INDEX_EXCLUDE_PATTERNS: 'autocode.indexing.excludePatterns',
    INDEX_MAX_FILE_SIZE: 'autocode.indexing.maxFileSize',
    
    // Web access configuration
    WEB_ACCESS_ENABLED: 'autocode.webAccess.enabled',
    WEB_ACCESS_TRUSTED_SOURCES: 'autocode.webAccess.trustedSources',
    WEB_ACCESS_TIMEOUT: 'autocode.webAccess.timeout',
    
    // UI configuration
    UI_THEME: 'autocode.ui.theme',
    UI_FONT_SIZE: 'autocode.ui.fontSize',
    UI_SHOW_STATUS_BAR: 'autocode.ui.showStatusBar',
    
    // Agent configuration
    AGENT_AUTO_EXECUTE: 'autocode.agent.autoExecute',
    AGENT_CONFIRMATION_REQUIRED: 'autocode.agent.confirmationRequired',
    
    // Terminal configuration
    TERMINAL_SHELL: 'autocode.terminal.shell',
    TERMINAL_SHOW_ON_COMMAND: 'autocode.terminal.showOnCommand'
};

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG = {
    // Model configuration
    MODEL_PATH: 'models/codegen-350M-mono',
    MODEL_MAX_TOKENS: 1024,
    MODEL_TEMPERATURE: 0.7,
    MODEL_TOP_P: 0.9,
    MODEL_REPETITION_PENALTY: 1.1,
    
    // Indexing configuration
    AUTO_INDEX_ON_STARTUP: true,
    INDEX_EXCLUDE_PATTERNS: ['node_modules', '.git', '.vscode', 'dist', 'out', 'build'],
    INDEX_MAX_FILE_SIZE: 1024 * 1024, // 1MB
    
    // Web access configuration
    WEB_ACCESS_ENABLED: true,
    WEB_ACCESS_TRUSTED_SOURCES: [
        'developer.mozilla.org',
        'docs.microsoft.com',
        'stackoverflow.com',
        'github.com',
        'npmjs.com',
        'pypi.org',
        'reactjs.org',
        'angular.io',
        'vuejs.org',
        'nodejs.org',
        'python.org'
    ],
    WEB_ACCESS_TIMEOUT: 10000, // 10 seconds
    
    // UI configuration
    UI_THEME: 'default',
    UI_FONT_SIZE: 14,
    UI_SHOW_STATUS_BAR: true,
    
    // Agent configuration
    AGENT_AUTO_EXECUTE: false,
    AGENT_CONFIRMATION_REQUIRED: true,
    
    // Terminal configuration
    TERMINAL_SHELL: 'default',
    TERMINAL_SHOW_ON_COMMAND: true
};

/**
 * Status bar constants
 */
export const STATUS_BAR = {
    MODEL_LOADING: '$(sync~spin) AutoCode: Loading...',
    MODEL_READY: '$(check) AutoCode: Ready',
    MODEL_ERROR: '$(error) AutoCode: Error',
    MODEL_NOT_LOADED: '$(warning) AutoCode: Not loaded',
    MODEL_GENERATING: '$(sync~spin) AutoCode: Generating...',
    
    INDEX_NOT_INDEXED: '$(database) AutoCode: Not indexed',
    INDEX_INDEXING: '$(database~spin) AutoCode: Indexing...',
    INDEX_INDEXED: '$(database) AutoCode: {0} files indexed',
    INDEX_ERROR: '$(database) AutoCode: Indexing failed',
    
    AGENT_IDLE: '$(person) AutoCode Agent: Idle',
    AGENT_RUNNING: '$(person~spin) AutoCode Agent: Running...',
    AGENT_COMPLETED: '$(person-check) AutoCode Agent: Completed',
    AGENT_ERROR: '$(person-error) AutoCode Agent: Error',
    
    AGENT_AUTO_IDLE: '$(rocket) AutoCode Agent Auto: Idle',
    AGENT_AUTO_RUNNING: '$(rocket~spin) AutoCode Agent Auto: Running...',
    AGENT_AUTO_COMPLETED: '$(rocket-check) AutoCode Agent Auto: Completed',
    AGENT_AUTO_ERROR: '$(rocket-error) AutoCode Agent Auto: Error'
};

/**
 * Webview constants
 */
export const WEBVIEW = {
    CHAT_VIEW_TYPE: 'autocode.chat',
    AGENT_VIEW_TYPE: 'autocode.agent',
    AGENT_AUTO_VIEW_TYPE: 'autocode.agentAuto',
    CONTEXT_VIEW_TYPE: 'autocode.context',
    SETTINGS_VIEW_TYPE: 'autocode.settings'
};

/**
 * Message constants
 */
export const MESSAGES = {
    EXTENSION_ACTIVATED: 'AutoCode extension is now active. Use the sidebar to start a chat or run commands.',
    EXTENSION_DEACTIVATED: 'AutoCode extension is now deactivated.',
    
    MODEL_LOADED: 'CodeGen model loaded successfully',
    MODEL_UNLOADED: 'CodeGen model unloaded',
    MODEL_RELOADED: 'Model reloaded successfully',
    MODEL_LOAD_ERROR: 'Failed to load model: {0}',
    MODEL_UNLOAD_ERROR: 'Failed to unload model: {0}',
    
    INDEX_CLEARED: 'Index cleared',
    INDEX_COMPLETED: 'Indexed {0} files in {1} seconds',
    INDEX_ERROR: 'Failed to index workspace: {0}',
    
    CONTEXT_ADDED: 'Added {0} as context',
    CONTEXT_REMOVED: 'Removed {0} from context',
    CONTEXT_CLEARED: 'Context cleared',
    
    AGENT_STARTED: 'Agent started with task: {0}',
    AGENT_COMPLETED: 'Agent completed task: {0}',
    AGENT_ERROR: 'Agent failed: {0}',
    
    AGENT_AUTO_STARTED: 'Agent Auto started with task: {0}',
    AGENT_AUTO_PAUSED: 'Agent Auto paused',
    AGENT_AUTO_RESUMED: 'Agent Auto resumed',
    AGENT_AUTO_STOPPED: 'Agent Auto stopped',
    AGENT_AUTO_COMPLETED: 'Agent Auto completed task: {0}',
    AGENT_AUTO_ERROR: 'Agent Auto failed: {0}',
    
    ACTION_APPROVED: 'Action approved: {0}',
    ACTION_REJECTED: 'Action rejected: {0}',
    ACTION_COMPLETED: 'Action completed: {0}',
    ACTION_FAILED: 'Action failed: {0}',
    
    NO_WORKSPACE: 'No workspace folder open',
    NO_EDITOR: 'No active editor',
    NO_SELECTION: 'No text selected'
};
