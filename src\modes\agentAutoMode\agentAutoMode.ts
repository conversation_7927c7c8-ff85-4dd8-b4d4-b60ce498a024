/**
 * Agent Auto mode implementation
 */

import * as vscode from 'vscode';
import { ModelService } from '../../services/modelService';
import { IndexService } from '../../services/indexService';
import { ContextService } from '../../services/contextService';
import { TerminalService } from '../../services/terminalService';
import { McpService } from '../../services/mcpService';
import { AgentAutoSession, AgentAutoAction } from '../../types';
import * as logger from '../../common/logger';
import * as utils from '../../common/utils';
import { STORAGE_KEYS } from '../../common/constants';
import { AgentAutoActionExecutor } from './agentAutoActionExecutor';
import { AgentAutoPlanner } from './agentAutoPlanner';
import { AgentAutoUI } from './agentAutoUI';

/**
 * Agent Auto mode implementation
 */
export class AgentAutoMode {
    private context: vscode.ExtensionContext;
    private modelService: ModelService;
    private indexService: IndexService;
    private contextService: ContextService;
    private terminalService: TerminalService;
    private mcpService: McpService;
    private currentSession: AgentAutoSession | null = null;
    private sessions: AgentAutoSession[] = [];
    private actionExecutor: AgentAutoActionExecutor;
    private planner: AgentAutoPlanner;
    private ui: AgentAutoUI;
    private disposables: vscode.Disposable[] = [];
    private isRunning: boolean = false;
    private isPaused: boolean = false;
    
    constructor(
        context: vscode.ExtensionContext,
        modelService: ModelService,
        indexService: IndexService,
        contextService: ContextService,
        terminalService: TerminalService,
        mcpService: McpService
    ) {
        this.context = context;
        this.modelService = modelService;
        this.indexService = indexService;
        this.contextService = contextService;
        this.terminalService = terminalService;
        this.mcpService = mcpService;
        
        // Create components
        this.actionExecutor = new AgentAutoActionExecutor(
            this.context,
            this.terminalService
        );
        
        this.planner = new AgentAutoPlanner(
            this.modelService,
            this.mcpService
        );
        
        this.ui = new AgentAutoUI(
            this.context,
            (message) => this.handleWebviewMessage(message)
        );
        
        // Load sessions from storage
        this.loadSessions();
        
        // Register commands
        this.registerCommands();
        
        logger.info('AgentAutoMode initialized');
    }
    
    /**
     * Start a new agent auto session
     * @param task The task description
     * @param autoExecute Whether to auto-execute actions
     */
    public async startNewSession(task: string, autoExecute: boolean = false): Promise<void> {
        try {
            // Create a new session
            const session: AgentAutoSession = {
                id: utils.generateId(),
                name: `Agent Auto ${new Date().toLocaleString()}`,
                task,
                actions: [],
                createdAt: Date.now(),
                updatedAt: Date.now(),
                status: 'in_progress',
                autoExecute
            };
            
            // Set as current session
            this.currentSession = session;
            
            // Add to sessions
            this.sessions.push(session);
            
            // Save sessions
            this.saveSessions();
            
            // Show agent auto UI
            this.ui.showUI(session);
            
            // Generate plan
            await this.generatePlan();
            
            // Start execution if auto-execute is enabled
            if (autoExecute) {
                await this.startExecution();
            }
            
            logger.info(`Started new agent auto session: ${task}`);
        } catch (error: any) {
            logger.error('Failed to start new agent auto session', error);
            throw error;
        }
    }
    
    /**
     * Continue an existing session
     * @param sessionId The session ID
     */
    public async continueSession(sessionId: string): Promise<void> {
        try {
            // Find the session
            const session = this.sessions.find(s => s.id === sessionId);
            if (!session) {
                throw new Error(`Session not found: ${sessionId}`);
            }
            
            // Set as current session
            this.currentSession = session;
            
            // Show agent auto UI
            this.ui.showUI(session);
            
            logger.info(`Continued agent auto session: ${session.name}`);
        } catch (error: any) {
            logger.error(`Failed to continue agent auto session: ${sessionId}`, error);
            throw error;
        }
    }
    
    /**
     * Generate a plan for the current task
     */
    private async generatePlan(): Promise<void> {
        try {
            // Check if there's a current session
            if (!this.currentSession) {
                throw new Error('No current session');
            }
            
            // Update UI
            this.ui.updateStatus('Generating plan...');
            
            // Generate plan
            const actions = await this.planner.generatePlan(this.currentSession.task);
            
            // Add actions to the session
            this.currentSession.actions = actions;
            this.currentSession.updatedAt = Date.now();
            
            // Update UI
            this.ui.updateSession(this.currentSession);
            
            // Save sessions
            this.saveSessions();
            
            logger.info(`Generated plan for task: ${this.currentSession.task}`);
        } catch (error: any) {
            logger.error('Failed to generate plan', error);
            throw error;
        }
    }
    
    /**
     * Start execution of the current session
     */
    public async startExecution(): Promise<void> {
        try {
            // Check if there's a current session
            if (!this.currentSession) {
                throw new Error('No current session');
            }
            
            // Check if already running
            if (this.isRunning) {
                return;
            }
            
            // Set running state
            this.isRunning = true;
            this.isPaused = false;
            
            // Update UI
            this.ui.updateStatus('Running...');
            
            // Execute actions
            await this.executeActions();
            
            // Set running state
            this.isRunning = false;
            
            // Update UI
            this.ui.updateStatus('Completed');
            
            // Update session status
            if (this.currentSession) {
                this.currentSession.status = 'completed';
                this.currentSession.updatedAt = Date.now();
                
                // Update UI
                this.ui.updateSession(this.currentSession);
                
                // Save sessions
                this.saveSessions();
            }
            
            logger.info('Execution completed');
        } catch (error: any) {
            // Set running state
            this.isRunning = false;
            
            // Update UI
            this.ui.updateStatus('Failed');
            
            // Update session status
            if (this.currentSession) {
                this.currentSession.status = 'failed';
                this.currentSession.updatedAt = Date.now();
                
                // Update UI
                this.ui.updateSession(this.currentSession);
                
                // Save sessions
                this.saveSessions();
            }
            
            logger.error('Execution failed', error);
            throw error;
        }
    }
    
    /**
     * Pause execution
     */
    public pauseExecution(): void {
        if (this.isRunning && !this.isPaused) {
            this.isPaused = true;
            this.ui.updateStatus('Paused');
            logger.info('Execution paused');
        }
    }
    
    /**
     * Resume execution
     */
    public async resumeExecution(): Promise<void> {
        if (this.isRunning && this.isPaused) {
            this.isPaused = false;
            this.ui.updateStatus('Running...');
            logger.info('Execution resumed');
            
            // Continue execution
            await this.executeActions();
        }
    }
    
    /**
     * Stop execution
     */
    public stopExecution(): void {
        if (this.isRunning) {
            this.isRunning = false;
            this.isPaused = false;
            this.ui.updateStatus('Stopped');
            logger.info('Execution stopped');
        }
    }
    
    /**
     * Execute actions
     */
    private async executeActions(): Promise<void> {
        // Check if there's a current session
        if (!this.currentSession) {
            return;
        }
        
        // Get pending actions
        const pendingActions = this.currentSession.actions.filter(
            action => action.status === 'pending'
        );
        
        // Execute each action
        for (const action of pendingActions) {
            // Check if execution is still running
            if (!this.isRunning) {
                break;
            }
            
            // Check if execution is paused
            if (this.isPaused) {
                return;
            }
            
            // Check if action can be executed
            if (!this.canExecuteAction(action)) {
                continue;
            }
            
            // Update action status
            action.status = 'approved';
            this.currentSession.updatedAt = Date.now();
            
            // Update UI
            this.ui.updateSession(this.currentSession);
            
            // Save sessions
            this.saveSessions();
            
            // Execute the action
            await this.executeAction(action);
        }
    }
    
    /**
     * Check if an action can be executed
     * @param action The action
     */
    private canExecuteAction(action: AgentAutoAction): boolean {
        // Check if the action has dependencies
        if (action.dependencies && action.dependencies.length > 0) {
            // Check if all dependencies are completed
            for (const dependencyId of action.dependencies) {
                const dependency = this.currentSession?.actions.find(a => a.id === dependencyId);
                
                if (!dependency || dependency.status !== 'completed') {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Execute an action
     * @param action The action
     */
    private async executeAction(action: AgentAutoAction): Promise<void> {
        try {
            // Execute the action
            await this.actionExecutor.executeAction(action);
            
            // Update action status
            action.status = 'completed';
            
            // Update session
            if (this.currentSession) {
                this.currentSession.updatedAt = Date.now();
                
                // Update UI
                this.ui.updateSession(this.currentSession);
                
                // Save sessions
                this.saveSessions();
            }
            
            logger.info(`Executed action: ${action.description}`);
        } catch (error: any) {
            // Update action status
            action.status = 'failed';
            action.error = error.message;
            
            // Update session
            if (this.currentSession) {
                this.currentSession.updatedAt = Date.now();
                
                // Update UI
                this.ui.updateSession(this.currentSession);
                
                // Save sessions
                this.saveSessions();
            }
            
            logger.error(`Failed to execute action: ${action.description}`, error);
            throw error;
        }
    }
    
    /**
     * Handle webview messages
     * @param message The message
     */
    private async handleWebviewMessage(message: any): Promise<void> {
        try {
            switch (message.command) {
                case 'startExecution':
                    await this.startExecution();
                    break;
                case 'pauseExecution':
                    this.pauseExecution();
                    break;
                case 'resumeExecution':
                    await this.resumeExecution();
                    break;
                case 'stopExecution':
                    this.stopExecution();
                    break;
                case 'approveAction':
                    await this.approveAction(message.actionId);
                    break;
                case 'rejectAction':
                    this.rejectAction(message.actionId);
                    break;
                case 'regeneratePlan':
                    await this.generatePlan();
                    break;
                case 'newSession':
                    const task = await vscode.window.showInputBox({
                        prompt: 'Enter the task description',
                        placeHolder: 'e.g., Create a React component for a login form'
                    });
                    
                    if (task) {
                        await this.startNewSession(task);
                    }
                    break;
                case 'continueSession':
                    await this.continueSession(message.sessionId);
                    break;
                case 'renameSession':
                    this.renameSession(message.sessionId, message.name);
                    break;
                case 'deleteSession':
                    this.deleteSession(message.sessionId);
                    break;
            }
        } catch (error: any) {
            logger.error('Failed to handle webview message', error);
            vscode.window.showErrorMessage(`Failed to handle webview message: ${error.message}`);
        }
    }
    
    /**
     * Approve an action
     * @param actionId The action ID
     */
    public async approveAction(actionId: string): Promise<void> {
        try {
            // Check if there's a current session
            if (!this.currentSession) {
                throw new Error('No current session');
            }
            
            // Find the action
            const action = this.currentSession.actions.find(a => a.id === actionId);
            if (!action) {
                throw new Error(`Action not found: ${actionId}`);
            }
            
            // Update action status
            action.status = 'approved';
            this.currentSession.updatedAt = Date.now();
            
            // Update UI
            this.ui.updateSession(this.currentSession);
            
            // Save sessions
            this.saveSessions();
            
            // Execute the action
            await this.executeAction(action);
            
            logger.info(`Approved action: ${action.description}`);
        } catch (error: any) {
            logger.error(`Failed to approve action: ${actionId}`, error);
            throw error;
        }
    }
    
    /**
     * Reject an action
     * @param actionId The action ID
     */
    public rejectAction(actionId: string): void {
        try {
            // Check if there's a current session
            if (!this.currentSession) {
                throw new Error('No current session');
            }
            
            // Find the action
            const action = this.currentSession.actions.find(a => a.id === actionId);
            if (!action) {
                throw new Error(`Action not found: ${actionId}`);
            }
            
            // Update action status
            action.status = 'rejected';
            this.currentSession.updatedAt = Date.now();
            
            // Update UI
            this.ui.updateSession(this.currentSession);
            
            // Save sessions
            this.saveSessions();
            
            logger.info(`Rejected action: ${action.description}`);
        } catch (error: any) {
            logger.error(`Failed to reject action: ${actionId}`, error);
            throw error;
        }
    }
    
    /**
     * Rename a session
     * @param sessionId The session ID
     * @param name The new name
     */
    private renameSession(sessionId: string, name: string): void {
        try {
            // Find the session
            const session = this.sessions.find(s => s.id === sessionId);
            if (!session) {
                throw new Error(`Session not found: ${sessionId}`);
            }
            
            // Update the name
            session.name = name;
            session.updatedAt = Date.now();
            
            // Save sessions
            this.saveSessions();
            
            // Update UI if it's the current session
            if (this.currentSession && this.currentSession.id === sessionId) {
                this.ui.updateSession(session);
            }
            
            logger.info(`Renamed session: ${sessionId} to ${name}`);
        } catch (error: any) {
            logger.error(`Failed to rename session: ${sessionId}`, error);
            throw error;
        }
    }
    
    /**
     * Delete a session
     * @param sessionId The session ID
     */
    private deleteSession(sessionId: string): void {
        try {
            // Find the session index
            const index = this.sessions.findIndex(s => s.id === sessionId);
            if (index === -1) {
                throw new Error(`Session not found: ${sessionId}`);
            }
            
            // Remove the session
            this.sessions.splice(index, 1);
            
            // Update current session if needed
            if (this.currentSession && this.currentSession.id === sessionId) {
                this.currentSession = this.sessions.length > 0 ? this.sessions[0] : null;
                
                // Update UI
                if (this.currentSession) {
                    this.ui.updateSession(this.currentSession);
                } else {
                    this.ui.clearUI();
                }
            }
            
            // Save sessions
            this.saveSessions();
            
            logger.info(`Deleted session: ${sessionId}`);
        } catch (error: any) {
            logger.error(`Failed to delete session: ${sessionId}`, error);
            throw error;
        }
    }
    
    /**
     * Load sessions from storage
     */
    private loadSessions(): void {
        try {
            // Get sessions from storage
            const sessionsJson = this.context.globalState.get<string>(STORAGE_KEYS.AGENT_AUTO_SESSIONS);
            if (sessionsJson) {
                this.sessions = JSON.parse(sessionsJson);
                logger.debug(`Loaded ${this.sessions.length} agent auto sessions`);
            }
        } catch (error) {
            logger.error('Failed to load agent auto sessions', error);
        }
    }
    
    /**
     * Save sessions to storage
     */
    private saveSessions(): void {
        try {
            // Save sessions to storage
            const sessionsJson = JSON.stringify(this.sessions);
            this.context.globalState.update(STORAGE_KEYS.AGENT_AUTO_SESSIONS, sessionsJson);
            logger.debug(`Saved ${this.sessions.length} agent auto sessions`);
        } catch (error) {
            logger.error('Failed to save agent auto sessions', error);
        }
    }
    
    /**
     * Register commands
     */
    private registerCommands(): void {
        // Register start agent auto command
        const startAgentAutoCommand = vscode.commands.registerCommand('autocode.startAgentAuto', async () => {
            try {
                const task = await vscode.window.showInputBox({
                    prompt: 'Enter the task description',
                    placeHolder: 'e.g., Create a React component for a login form'
                });
                
                if (task) {
                    const autoExecute = await vscode.window.showQuickPick(['Yes', 'No'], {
                        placeHolder: 'Auto-execute actions?'
                    });
                    
                    await this.startNewSession(task, autoExecute === 'Yes');
                }
            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to start agent auto: ${error.message}`);
            }
        });
        
        // Register pause agent auto command
        const pauseAgentAutoCommand = vscode.commands.registerCommand('autocode.pauseAgentAuto', () => {
            try {
                this.pauseExecution();
            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to pause agent auto: ${error.message}`);
            }
        });
        
        // Register resume agent auto command
        const resumeAgentAutoCommand = vscode.commands.registerCommand('autocode.resumeAgentAuto', async () => {
            try {
                await this.resumeExecution();
            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to resume agent auto: ${error.message}`);
            }
        });
        
        // Register stop agent auto command
        const stopAgentAutoCommand = vscode.commands.registerCommand('autocode.stopAgentAuto', () => {
            try {
                this.stopExecution();
            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to stop agent auto: ${error.message}`);
            }
        });
        
        // Add to disposables
        this.disposables.push(
            startAgentAutoCommand,
            pauseAgentAutoCommand,
            resumeAgentAutoCommand,
            stopAgentAutoCommand
        );
    }
    
    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Stop execution
        this.stopExecution();
        
        // Dispose of UI
        this.ui.dispose();
        
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.disposables = [];
    }
}
