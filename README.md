# AutoCode VSCode Extension

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![VSCode](https://img.shields.io/badge/VSCode-007ACC?logo=visual-studio-code&logoColor=white)](https://code.visualstudio.com/)

A powerful AI-powered VSCode extension that brings intelligent code generation, autonomous coding assistance, and context-aware development support directly to your editor.

## 🚀 Overview

AutoCode transforms your VSCode experience with advanced AI capabilities powered by the local CodeGen-350M-Mono model. Whether you're learning to code, working on complex projects, or need autonomous assistance, AutoCode adapts to your workflow with three distinct operational modes and comprehensive codebase understanding.

**Key Highlights:**
- 🧠 **Local AI Model** - No data leaves your machine
- 📚 **Codebase Indexing** - Deep understanding of your project
- 🎯 **Three Operational Modes** - From chat to full automation
- 🔧 **Terminal Integration** - Execute commands through natural language
- 🌐 **Internet Access** - Access latest documentation and examples
- 📝 **Context Management** - Focus AI attention on relevant code

## ✨ Key Features

### 🎯 Three Operational Modes

| Mode | Description | Use Case |
|------|-------------|----------|
| **💬 Chat Mode** | Interactive Q&A without code changes | Learning, debugging, explanations |
| **🤖 Agent Mode** | AI actions with user confirmation | Guided development, code generation |
| **⚡ Agent Auto Mode** | Fully autonomous operation | Complex tasks, project setup |

### 📚 Intelligent Codebase Understanding
- **Automatic Indexing** - Analyzes your workspace structure and relationships
- **Symbol Recognition** - Understands functions, classes, variables, and dependencies
- **Context Awareness** - Provides relevant suggestions based on your project
- **Real-time Updates** - Keeps index current as you modify code

### 🔧 Advanced Capabilities
- **🖥️ Terminal Integration** - Execute commands through natural language
- **📝 Context Management** - Focus AI on specific files or folders
- **🌐 Internet Access** - Retrieve latest documentation and examples
- **💾 Conversation History** - Save and organize your interactions
- **✨ Prompt Enhancement** - Optimize your queries for better results
- **🔗 MCP Integration** - Enhanced context and source management

## 🚀 Quick Start

### Prerequisites
- **VSCode** 1.60.0 or higher
- **Node.js** 16.0 or higher
- **4GB RAM** minimum
- **1GB disk space** for model storage

## Building and Running Locally

The extension must be built before it can be run locally. Follow these steps:

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-username/autocode-vscode-extension.git
   cd autocode-vscode-extension
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Build the Extension**
   ```bash
   npm run build
   # or for TypeScript compilation only
   npm run compile
   ```

4. **Run in Development Mode**
   - Press `F5` in VS Code to start debugging
   - A new VS Code window will open with the extension loaded

5. **Verify Build (Optional)**
   ```bash
   npm run verify
   ```

### First Steps
1. **Open a workspace** in the new VSCode window
2. **Wait for indexing** to complete (status bar indicator)
3. **Start chatting** with `Ctrl+Shift+P` → "AutoCode: Start Chat Mode"

## 🎯 Usage Examples

### 💬 Chat Mode - Learning & Understanding
```
You: "Explain how authentication works in this codebase"
AutoCode: Analyzes your auth files and explains the flow...

You: "What's the best way to handle async errors in JavaScript?"
AutoCode: Provides examples and best practices...
```

### 🤖 Agent Mode - Guided Development
```
You: "Create a React component for user profile"
AutoCode:
1. Proposes file structure
2. Shows component code
3. Waits for your approval
4. Creates files after confirmation
```

### ⚡ Agent Auto Mode - Full Automation
```
You: "Set up a complete Express.js API with authentication"
AutoCode:
✅ Creates project structure
✅ Installs dependencies
✅ Sets up routes and middleware
✅ Configures authentication
✅ Adds error handling
```

## 🛠️ Technical Specifications

| Component | Details |
|-----------|---------|
| **AI Model** | CodeGen-350M-Mono (350M parameters) |
| **Runtime** | Local execution, no cloud dependencies |
| **Languages** | JavaScript, TypeScript, Python, Java, C++, and more |
| **Platforms** | Windows, macOS, Linux |
| **Architecture** | Modular service-based design |

## 📚 Documentation

| Document | Description |
|----------|-------------|
| **[Installation Guide](docs/INSTALLATION.md)** | Detailed setup instructions |
| **[User Guide](docs/USER_GUIDE.md)** | Complete usage documentation |
| **[Configuration](docs/CONFIGURATION.md)** | Settings and customization |
| **[Operational Modes](docs/OPERATIONAL_MODES.md)** | Mode-specific features |
| **[API Reference](docs/API_REFERENCE.md)** | Technical API documentation |
| **[Development Guide](docs/DEVELOPMENT.md)** | Contributing and development |
| **[Troubleshooting](docs/TROUBLESHOOTING.md)** | Common issues and solutions |

## 🔧 Development Status

### ✅ Completed Features
- [x] Core extension architecture
- [x] CodeGen-350M-Mono integration
- [x] Codebase indexing system
- [x] All three operational modes
- [x] Context management
- [x] Terminal integration
- [x] Internet access capabilities
- [x] MCP integration
- [x] Configuration system

### 🚧 In Progress
- [ ] UI/UX enhancements
- [ ] Performance optimizations
- [ ] Additional language support
- [ ] Advanced debugging features

### 🎯 Planned Features
- [ ] Team collaboration features
- [ ] Cloud synchronization
- [ ] Custom model support
- [ ] Plugin ecosystem

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](docs/DEVELOPMENT.md) for details on:
- Setting up the development environment
- Code style and conventions
- Testing procedures
- Submitting pull requests

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Salesforce** for the CodeGen-350M-Mono model
- **Microsoft** for the VSCode extension API
- **The open-source community** for inspiration and tools

---

**Made with ❤️ for developers who want AI-powered coding assistance without compromising privacy.**
