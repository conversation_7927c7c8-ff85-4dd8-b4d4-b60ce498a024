# AutoCode VSCode Extension

A powerful local VSCode extension leveraging the CodeGen-350M-Mono model for intelligent code generation, assistance, and autonomous coding.

## Overview

AutoCode is a VSCode extension that provides advanced AI-powered coding assistance directly within your editor. By utilizing the local CodeGen-350M-Mono model, it offers intelligent code generation, answers coding questions, and can even autonomously create, modify, and manage code files based on your instructions.

The extension indexes your codebase to provide context-aware assistance, making it particularly effective for working within existing projects. With three distinct operational modes, AutoCode adapts to your preferred level of interaction and automation.

## Key Features

### 1. Codebase Indexing
- Automatically indexes your current workspace when activated
- Builds a comprehensive understanding of your project structure
- Enables context-aware responses and suggestions
- Continuously updates as your codebase evolves

### 2. Multiple Operational Modes

#### Chat Mode
- Ask questions about your codebase
- Get explanations and suggestions
- Receive code snippets and examples
- Perfect for learning and understanding code

#### Agent Mode
- Request code creation, modification, or deletion
- Execute terminal commands through natural language
- Requires confirmation for each action
- Maintains user control while automating routine tasks

#### Agent Auto Mode
- Fully autonomous operation
- Completes complex coding tasks without intervention
- Handles file operations and terminal commands independently
- Ideal for repetitive tasks or generating boilerplate code

### 3. Context Management
- Add specific folders or files as context
- Focus the AI's attention on relevant parts of your codebase
- Improve response accuracy with targeted context
- Support for various context types

### 4. Conversation Management
- Save and organize discussions by mode (Chat, Agent, Agent Auto)
- Review past conversations with full context
- Continue previous discussions seamlessly
- Sort by recency, topic, or project
- Export conversations for sharing or documentation
- Search through conversation history

### 5. Smart Terminal Management
- Execute terminal commands through natural language
- Maintain context across command sequences
- Complete multi-step processes (e.g., project generation, dependency installation)
- Intelligent handling of command outputs

### 6. Prompt Enhancement
- Magic button to optimize your prompts
- Improve query clarity and specificity
- Get better results with refined instructions
- Automatically correct ambiguous or unclear queries

### 7. Internet Access
- Retrieve information from online resources when needed
- Access documentation, examples, and best practices
- Stay updated with the latest programming knowledge

### 8. MCP (Model Context Protocol) Integration
- Enhanced context management
- Improved source access capabilities
- Structured information retrieval
- Better handling of complex information sources

## Technical Details

### Model Information
- **Model**: CodeGen-350M-Mono
- **Size**: 350 million parameters
- **Specialization**: Code generation and understanding
- **Location**: Local (models/codegen-350M-mono)

### System Requirements
- VSCode version 1.60.0 or higher
- Minimum 4GB RAM
- 1GB disk space for model storage
- Windows/macOS/Linux compatible

## Usage Examples

### Chat Mode
In Chat mode, users can ask questions about programming concepts, get explanations of code, and receive guidance without making changes to their codebase. AutoCode provides detailed explanations and examples based on the context of the user's project.

### Agent Mode
In Agent mode, users can request code creation, modifications, and terminal operations. AutoCode will propose a detailed plan and wait for user confirmation before executing each step. This provides a balance between automation and user control, ensuring that all changes are reviewed and approved.

### Agent Auto Mode
In Agent Auto mode, users can request complex tasks that AutoCode will execute autonomously from start to finish. This includes setting up projects, implementing features, and performing system operations. AutoCode handles all steps independently and provides a summary of actions upon completion, maximizing efficiency for repetitive or well-defined tasks.

## Installation

1. Clone this repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build`
4. Install the extension in VSCode:
   - Press F5 to run in development mode, or
   - Package the extension: `vsce package`
   - Install the generated .vsix file

## Development Roadmap

- [ ] Initial extension setup
- [ ] Model integration
- [ ] Codebase indexing implementation
- [ ] Chat mode functionality
- [ ] Agent mode with confirmation flow
- [ ] Agent Auto mode
- [ ] Context management
- [ ] Conversation history and management
- [ ] Terminal command execution
- [ ] Prompt enhancement
- [ ] Internet access integration
- [ ] MCP integration
- [ ] UI/UX refinement
- [ ] Performance optimization
- [ ] Documentation and examples

## Detailed Documentation

For more detailed information about specific features, please refer to the following documentation:

- [Codebase Indexing](docs/CODEBASE_INDEXING.md)
- [Operational Modes](docs/OPERATIONAL_MODES.md)
- [Terminal Management](docs/TERMINAL_MANAGEMENT.md)
- [MCP Integration](docs/MCP_INTEGRATION.md)
- [Internet Access](docs/INTERNET_ACCESS.md)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

[MIT License](LICENSE)
