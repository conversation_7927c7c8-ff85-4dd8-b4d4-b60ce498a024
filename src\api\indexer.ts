/**
 * Codebase indexing functionality
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

// Define the index entry
export interface IndexEntry {
    uri: vscode.Uri;
    fileName: string;
    filePath: string;
    fileExtension: string;
    content: string;
    symbols: Symbol[];
    lastModified: number;
}

// Define the symbol
export interface Symbol {
    name: string;
    kind: vscode.SymbolKind;
    range: vscode.Range;
    detail?: string;
    containerName?: string;
}

// Define the index query
export interface IndexQuery {
    text?: string;
    filePattern?: string;
    symbolKind?: vscode.SymbolKind;
    maxResults?: number;
}

// Define the index result
export interface IndexResult {
    entries: IndexEntry[];
    totalFiles: number;
    indexedFiles: number;
    skippedFiles: number;
    elapsedTime: number;
}

// Define the query result
export interface QueryResult {
    entries: IndexEntry[];
    symbols: Symbol[];
    totalResults: number;
    elapsedTime: number;
}

/**
 * Class for indexing the codebase
 */
export class Indexer {
    private index: Map<string, IndexEntry> = new Map();
    private indexing: boolean = false;
    private lastIndexTime: number = 0;
    private excludePatterns: RegExp[] = [
        /node_modules/,
        /\.git/,
        /\.vscode/,
        /dist/,
        /out/,
        /build/
    ];

    constructor() {
        // Initialize the indexer
    }

    /**
     * Index the workspace
     * @param progress Progress reporter
     */
    public async indexWorkspace(progress?: vscode.Progress<{ message?: string; increment?: number }>): Promise<IndexResult> {
        if (this.indexing) {
            throw new Error('Indexing already in progress');
        }

        this.indexing = true;
        const startTime = Date.now();
        const result: IndexResult = {
            entries: [],
            totalFiles: 0,
            indexedFiles: 0,
            skippedFiles: 0,
            elapsedTime: 0
        };

        try {
            // Get all workspace folders
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                throw new Error('No workspace folder open');
            }

            // Clear the index
            this.index.clear();

            // Process each workspace folder
            for (const folder of workspaceFolders) {
                await this.indexFolder(folder.uri, result, progress);
            }

            // Update the result
            result.entries = Array.from(this.index.values());
            result.elapsedTime = Date.now() - startTime;
            this.lastIndexTime = Date.now();

            return result;
        } finally {
            this.indexing = false;
        }
    }

    /**
     * Index a folder
     * @param folderUri The folder URI
     * @param result The index result
     * @param progress Progress reporter
     */
    private async indexFolder(
        folderUri: vscode.Uri,
        result: IndexResult,
        progress?: vscode.Progress<{ message?: string; increment?: number }>
    ): Promise<void> {
        // Get all files in the folder
        const files = await vscode.workspace.findFiles(
            new vscode.RelativePattern(folderUri, '**/*'),
            '{' + this.excludePatterns.map(p => p.source).join(',') + '}'
        );

        result.totalFiles += files.length;

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // Skip directories
            if (fs.statSync(file.fsPath).isDirectory()) {
                result.skippedFiles++;
                continue;
            }

            // Skip excluded files
            if (this.shouldExcludeFile(file.fsPath)) {
                result.skippedFiles++;
                continue;
            }

            // Update progress
            if (progress) {
                progress.report({
                    message: `Indexing ${path.basename(file.fsPath)}`,
                    increment: 100 / files.length
                });
            }

            try {
                // Index the file
                await this.indexFile(file);
                result.indexedFiles++;
            } catch (error) {
                console.error(`Error indexing file ${file.fsPath}:`, error);
                result.skippedFiles++;
            }
        }
    }

    /**
     * Index a file
     * @param fileUri The file URI
     */
    private async indexFile(fileUri: vscode.Uri): Promise<void> {
        // Read the file content
        const content = await fs.promises.readFile(fileUri.fsPath, 'utf8');
        
        // Get file information
        const fileName = path.basename(fileUri.fsPath);
        const fileExtension = path.extname(fileUri.fsPath).toLowerCase();
        const filePath = fileUri.fsPath;
        const lastModified = fs.statSync(fileUri.fsPath).mtimeMs;

        // Get symbols from the file
        const symbols = await this.getSymbols(fileUri);

        // Create the index entry
        const entry: IndexEntry = {
            uri: fileUri,
            fileName,
            filePath,
            fileExtension,
            content,
            symbols,
            lastModified
        };

        // Add to the index
        this.index.set(fileUri.toString(), entry);
    }

    /**
     * Get symbols from a file
     * @param fileUri The file URI
     */
    private async getSymbols(fileUri: vscode.Uri): Promise<Symbol[]> {
        try {
            // Use VSCode's document symbol provider
            const document = await vscode.workspace.openTextDocument(fileUri);
            const symbolProvider = vscode.languages.getLanguageConfiguration(document.languageId);
            
            if (!symbolProvider) {
                return [];
            }

            // Get symbols
            const vscodeSymbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                'vscode.executeDocumentSymbolProvider',
                fileUri
            );

            if (!vscodeSymbols || vscodeSymbols.length === 0) {
                return [];
            }

            // Convert to our symbol format
            const symbols: Symbol[] = [];
            this.flattenSymbols(vscodeSymbols, symbols, '');

            return symbols;
        } catch (error) {
            console.error(`Error getting symbols for ${fileUri.fsPath}:`, error);
            return [];
        }
    }

    /**
     * Flatten symbols
     * @param vscodeSymbols VSCode symbols
     * @param symbols Our symbols
     * @param containerName Container name
     */
    private flattenSymbols(
        vscodeSymbols: vscode.DocumentSymbol[],
        symbols: Symbol[],
        containerName: string
    ): void {
        for (const vscodeSymbol of vscodeSymbols) {
            // Create our symbol
            const symbol: Symbol = {
                name: vscodeSymbol.name,
                kind: vscodeSymbol.kind,
                range: vscodeSymbol.range,
                detail: vscodeSymbol.detail,
                containerName
            };

            // Add to the list
            symbols.push(symbol);

            // Process children
            if (vscodeSymbol.children && vscodeSymbol.children.length > 0) {
                this.flattenSymbols(vscodeSymbol.children, symbols, vscodeSymbol.name);
            }
        }
    }

    /**
     * Check if a file should be excluded
     * @param filePath The file path
     */
    private shouldExcludeFile(filePath: string): boolean {
        // Check against exclude patterns
        for (const pattern of this.excludePatterns) {
            if (pattern.test(filePath)) {
                return true;
            }
        }

        // Check file size (skip files larger than 1MB)
        const stats = fs.statSync(filePath);
        if (stats.size > 1024 * 1024) {
            return true;
        }

        return false;
    }

    /**
     * Query the index
     * @param query The query
     */
    public async query(query: IndexQuery): Promise<QueryResult> {
        const startTime = Date.now();
        const result: QueryResult = {
            entries: [],
            symbols: [],
            totalResults: 0,
            elapsedTime: 0
        };

        // Check if index is empty
        if (this.index.size === 0) {
            result.elapsedTime = Date.now() - startTime;
            return result;
        }

        // Get all entries
        const entries = Array.from(this.index.values());
        
        // Filter by file pattern
        let filteredEntries = entries;
        if (query.filePattern) {
            const pattern = new RegExp(query.filePattern, 'i');
            filteredEntries = filteredEntries.filter(entry => 
                pattern.test(entry.fileName) || pattern.test(entry.filePath)
            );
        }

        // Filter by text
        if (query.text) {
            const text = query.text.toLowerCase();
            filteredEntries = filteredEntries.filter(entry => 
                entry.content.toLowerCase().includes(text) ||
                entry.symbols.some(symbol => symbol.name.toLowerCase().includes(text))
            );
        }

        // Filter by symbol kind
        let filteredSymbols: Symbol[] = [];
        if (query.symbolKind !== undefined) {
            for (const entry of filteredEntries) {
                const symbols = entry.symbols.filter(symbol => symbol.kind === query.symbolKind);
                filteredSymbols.push(...symbols);
            }
        } else {
            for (const entry of filteredEntries) {
                filteredSymbols.push(...entry.symbols);
            }
        }

        // Limit results
        if (query.maxResults && filteredEntries.length > query.maxResults) {
            filteredEntries = filteredEntries.slice(0, query.maxResults);
        }

        // Update the result
        result.entries = filteredEntries;
        result.symbols = filteredSymbols;
        result.totalResults = filteredEntries.length;
        result.elapsedTime = Date.now() - startTime;

        return result;
    }

    /**
     * Get the index statistics
     */
    public getStatistics(): { totalFiles: number; lastIndexTime: number } {
        return {
            totalFiles: this.index.size,
            lastIndexTime: this.lastIndexTime
        };
    }

    /**
     * Check if indexing is in progress
     */
    public isIndexing(): boolean {
        return this.indexing;
    }

    /**
     * Clear the index
     */
    public clearIndex(): void {
        this.index.clear();
        this.lastIndexTime = 0;
    }
}
