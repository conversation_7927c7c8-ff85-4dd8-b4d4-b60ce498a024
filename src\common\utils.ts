/**
 * Common utilities used throughout the extension
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Generate a unique ID
 */
export function generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Format a date as a string
 * @param date The date to format
 */
export function formatDate(date: Date | number): string {
    if (typeof date === 'number') {
        date = new Date(date);
    }
    return date.toLocaleString();
}

/**
 * Get the workspace folder
 */
export function getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
    return vscode.workspace.workspaceFolders?.[0];
}

/**
 * Get the full path for a file in the workspace
 * @param relativePath The relative path
 */
export function getWorkspacePath(relativePath: string): string {
    const workspaceFolder = getWorkspaceFolder();
    if (!workspaceFolder) {
        throw new Error('No workspace folder open');
    }
    return path.join(workspaceFolder.uri.fsPath, relativePath);
}

/**
 * Check if a file exists
 * @param filePath The file path
 */
export function fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
}

/**
 * Create a directory if it doesn't exist
 * @param dirPath The directory path
 */
export function ensureDirectory(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

/**
 * Read a file
 * @param filePath The file path
 */
export function readFile(filePath: string): string {
    return fs.readFileSync(filePath, 'utf8');
}

/**
 * Write a file
 * @param filePath The file path
 * @param content The file content
 */
export function writeFile(filePath: string, content: string): void {
    ensureDirectory(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
}

/**
 * Delete a file
 * @param filePath The file path
 */
export function deleteFile(filePath: string): void {
    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
    }
}

/**
 * Show an error message
 * @param message The error message
 * @param error The error object
 */
export function showError(message: string, error?: any): void {
    const errorMessage = error ? `${message}: ${error.message}` : message;
    vscode.window.showErrorMessage(errorMessage);
    console.error(message, error);
}

/**
 * Show an information message
 * @param message The information message
 */
export function showInfo(message: string): void {
    vscode.window.showInformationMessage(message);
}

/**
 * Show a warning message
 * @param message The warning message
 */
export function showWarning(message: string): void {
    vscode.window.showWarningMessage(message);
}

/**
 * Show a progress notification
 * @param title The progress title
 * @param task The task to run
 */
export async function showProgress<T>(title: string, task: (progress: vscode.Progress<{ message?: string; increment?: number }>) => Promise<T>): Promise<T> {
    return vscode.window.withProgress<T>(
        {
            location: vscode.ProgressLocation.Notification,
            title,
            cancellable: false
        },
        task
    );
}

/**
 * Debounce a function
 * @param func The function to debounce
 * @param wait The wait time in milliseconds
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;
    
    return function(...args: Parameters<T>): void {
        const later = () => {
            timeout = null;
            func(...args);
        };
        
        if (timeout) {
            clearTimeout(timeout);
        }
        
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle a function
 * @param func The function to throttle
 * @param limit The limit in milliseconds
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
    let inThrottle = false;
    
    return function(...args: Parameters<T>): void {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => {
                inThrottle = false;
            }, limit);
        }
    };
}

/**
 * Sleep for a specified time
 * @param ms The time to sleep in milliseconds
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Truncate a string to a specified length
 * @param str The string to truncate
 * @param maxLength The maximum length
 */
export function truncate(str: string, maxLength: number): string {
    if (str.length <= maxLength) {
        return str;
    }
    return str.substring(0, maxLength) + '...';
}

/**
 * Escape HTML special characters
 * @param html The HTML string to escape
 */
export function escapeHtml(html: string): string {
    return html
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

/**
 * Get the extension context
 * @param context The extension context
 */
export function getExtensionContext(context: vscode.ExtensionContext): vscode.ExtensionContext {
    return context;
}

/**
 * Get the extension path
 * @param context The extension context
 * @param relativePath The relative path
 */
export function getExtensionPath(context: vscode.ExtensionContext, relativePath: string): string {
    return path.join(context.extensionPath, relativePath);
}

/**
 * Get the extension URI
 * @param context The extension context
 * @param relativePath The relative path
 */
export function getExtensionUri(context: vscode.ExtensionContext, relativePath: string): vscode.Uri {
    return vscode.Uri.file(getExtensionPath(context, relativePath));
}

/**
 * Register a command
 * @param context The extension context
 * @param command The command name
 * @param callback The command callback
 */
export function registerCommand(context: vscode.ExtensionContext, command: string, callback: (...args: any[]) => any): void {
    const disposable = vscode.commands.registerCommand(command, callback);
    context.subscriptions.push(disposable);
}
