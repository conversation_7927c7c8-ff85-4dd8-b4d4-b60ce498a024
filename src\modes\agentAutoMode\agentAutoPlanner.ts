/**
 * Agent Auto Planner
 */

import { ModelService } from '../../services/modelService';
import { McpService } from '../../services/mcpService';
import { AgentAutoAction } from '../../types';
import * as logger from '../../common/logger';
import * as utils from '../../common/utils';

/**
 * Agent Auto Planner
 */
export class AgentAutoPlanner {
    private modelService: ModelService;
    private mcpService: McpService;
    
    constructor(
        modelService: ModelService,
        mcpService: McpService
    ) {
        this.modelService = modelService;
        this.mcpService = mcpService;
    }
    
    /**
     * Generate a plan for a task
     * @param task The task description
     */
    public async generatePlan(task: string): Promise<AgentAutoAction[]> {
        try {
            logger.debug(`Generating plan for task: ${task}`);
            
            // Get context information
            const contextInfo = await this.getContextInfo(task);
            
            // Prepare the prompt
            const prompt = this.preparePlanningPrompt(task, contextInfo);
            
            // Generate text
            const response = await this.modelService.generateText({
                prompt,
                maxTokens: 2048,
                temperature: 0.7
            });
            
            // Parse the response to extract actions
            const actions = this.parseActionsFromResponse(response.text);
            
            logger.info(`Generated plan with ${actions.length} actions for task: ${task}`);
            
            return actions;
        } catch (error: any) {
            logger.error(`Failed to generate plan for task: ${task}`, error);
            throw error;
        }
    }
    
    /**
     * Get context information for a task
     * @param task The task description
     */
    private async getContextInfo(task: string): Promise<string> {
        try {
            // Query the MCP service
            const result = await this.mcpService.query({
                text: task,
                maxResults: 5
            });
            
            // Extract relevant information
            let contextInfo = '';
            
            for (const item of result.results) {
                contextInfo += `Source: ${item.sourceId}\n`;
                contextInfo += `Content: ${item.content}\n\n`;
            }
            
            return contextInfo;
        } catch (error: any) {
            logger.warn(`Failed to get context info for task: ${task}`, error);
            return '';
        }
    }
    
    /**
     * Prepare the planning prompt
     * @param task The task description
     * @param contextInfo The context information
     */
    private preparePlanningPrompt(task: string, contextInfo: string): string {
        return `
Task: ${task}

${contextInfo ? `Context Information:\n${contextInfo}\n` : ''}

Generate a step-by-step plan to accomplish this task. For each step, specify the action type (file_create, file_update, file_delete, terminal_command) and provide details.

For file_create and file_update actions, include the file path and content.
For file_delete actions, include the file path.
For terminal_command actions, include the command to execute.

Also, specify dependencies between actions if applicable.

Format your response as follows:

Action 1:
Type: [action_type]
Description: [description]
Details:
  [key1]: [value1]
  [key2]: [value2]
Dependencies: [list of action numbers that must be completed before this one, if any]

Action 2:
...

Please provide a comprehensive and detailed plan.
`;
    }
    
    /**
     * Parse actions from the model response
     * @param text The response text
     */
    private parseActionsFromResponse(text: string): AgentAutoAction[] {
        // This is a simplified implementation
        // In a real implementation, you would use a more sophisticated parsing approach
        
        const actions: AgentAutoAction[] = [];
        
        // Split the text into action blocks
        const actionBlocks = text.split(/Action \d+:/g).filter(block => block.trim().length > 0);
        
        // Parse each action block
        for (let i = 0; i < actionBlocks.length; i++) {
            const block = actionBlocks[i].trim();
            
            // Extract action type
            const typeMatch = block.match(/Type:\s*(\w+)/);
            if (!typeMatch) {
                continue;
            }
            
            const type = typeMatch[1].toLowerCase() as AgentAutoAction['type'];
            if (!['file_create', 'file_update', 'file_delete', 'terminal_command'].includes(type)) {
                continue;
            }
            
            // Extract description
            const descriptionMatch = block.match(/Description:\s*(.+?)(?=\n|$)/);
            const description = descriptionMatch ? descriptionMatch[1].trim() : `Action ${i + 1}`;
            
            // Extract details
            const details: any = {};
            
            if (type === 'file_create' || type === 'file_update') {
                // Extract file path
                const filePathMatch = block.match(/file[pP]ath:\s*(.+?)(?=\n|$)/);
                if (filePathMatch) {
                    details.filePath = filePathMatch[1].trim();
                }
                
                // Extract file content
                const fileContentMatch = block.match(/(?:file)?[cC]ontent:\s*(.+?)(?=\n\n|$)/s);
                if (fileContentMatch) {
                    details.fileContent = fileContentMatch[1].trim();
                }
            } else if (type === 'file_delete') {
                // Extract file path
                const filePathMatch = block.match(/file[pP]ath:\s*(.+?)(?=\n|$)/);
                if (filePathMatch) {
                    details.filePath = filePathMatch[1].trim();
                }
            } else if (type === 'terminal_command') {
                // Extract command
                const commandMatch = block.match(/[cC]ommand:\s*(.+?)(?=\n|$)/);
                if (commandMatch) {
                    details.command = commandMatch[1].trim();
                }
                
                // Extract working directory
                const cwdMatch = block.match(/[cC]wd:\s*(.+?)(?=\n|$)/);
                if (cwdMatch) {
                    details.cwd = cwdMatch[1].trim();
                }
            }
            
            // Extract dependencies
            const dependenciesMatch = block.match(/[dD]ependencies:\s*(.+?)(?=\n|$)/);
            const dependencies = dependenciesMatch
                ? dependenciesMatch[1].trim().split(/,\s*/).map(d => d.trim())
                : [];
            
            // Create the action
            const action: AgentAutoAction = {
                id: utils.generateId(),
                type,
                description,
                details,
                status: 'pending',
                priority: i + 1,
                dependencies,
                timestamp: Date.now()
            };
            
            actions.push(action);
        }
        
        // If no actions were found, create a placeholder action
        if (actions.length === 0) {
            actions.push({
                id: utils.generateId(),
                type: 'terminal_command',
                description: 'Analyze the task',
                details: {
                    command: 'echo "Analyzing task..."'
                },
                status: 'pending',
                priority: 1,
                dependencies: [],
                timestamp: Date.now()
            });
        }
        
        return actions;
    }
}
