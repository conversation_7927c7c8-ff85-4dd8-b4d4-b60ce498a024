# Troubleshooting Guide

This guide helps you resolve common issues with the AutoCode VSCode Extension.

## Quick Diagnostics

### Check Extension Status
1. Open Command Palette (`Ctrl+Shift+P`)
2. Run "AutoCode: Show System Status"
3. Review all component statuses

### Status Bar Indicators
| Indicator | Status | Meaning |
|-----------|--------|---------|
| 🟢 AutoCode: Ready | Good | Extension fully operational |
| 🟡 AutoCode: Loading | Warning | Components still initializing |
| 🔴 AutoCode: Error | Error | Critical issue needs attention |

## Common Issues

### Model Issues

#### Problem: Model Not Loading
**Symptoms:**
- Status bar shows "Model: Error" or "Model: Not Loaded"
- Chat responses show "Model not available"

**Solutions:**
1. **Check Model Files**
   ```bash
   # Verify model directory exists
   ls -la models/codegen-350M-mono/
   
   # Required files:
   # - model.onnx (or equivalent)
   # - tokenizer.json
   # - vocab.json
   # - config.json
   ```

2. **Reload Model**
   - Command Palette: "AutoCode: Reload Model"
   - Wait for completion (check status bar)

3. **Check File Permissions**
   ```bash
   # Fix permissions if needed
   chmod -R 755 models/
   ```

4. **Verify Configuration**
   ```json
   {
     "autocode.model.path": "models/codegen-350M-mono"
   }
   ```

#### Problem: Out of Memory During Model Loading
**Symptoms:**
- Extension crashes during startup
- "Insufficient memory" error messages

**Solutions:**
1. **Close Other Applications**
   - Free up system RAM
   - Close unnecessary browser tabs

2. **Adjust Model Settings**
   ```json
   {
     "autocode.model.maxTokens": 512,
     "autocode.model.batchSize": 1
   }
   ```

3. **Increase Virtual Memory**
   - Windows: Increase page file size
   - macOS/Linux: Increase swap space

### Indexing Issues

#### Problem: Indexing Fails or Takes Too Long
**Symptoms:**
- Status bar stuck on "Index: Indexing"
- High CPU usage for extended periods
- "Indexing failed" error messages

**Solutions:**
1. **Exclude Large Directories**
   ```json
   {
     "autocode.indexing.excludePatterns": [
       "**/node_modules/**",
       "**/dist/**",
       "**/build/**",
       "**/coverage/**",
       "**/.git/**",
       "**/vendor/**"
     ]
   }
   ```

2. **Reduce File Size Limit**
   ```json
   {
     "autocode.indexing.maxFileSize": 524288
   }
   ```

3. **Clear Index and Restart**
   - Command Palette: "AutoCode: Clear Index"
   - Command Palette: "AutoCode: Index Workspace"

#### Problem: Index Not Updating
**Symptoms:**
- Changes to files not reflected in AI responses
- Outdated code suggestions

**Solutions:**
1. **Force Reindex**
   - Command Palette: "AutoCode: Clear Index"
   - Command Palette: "AutoCode: Index Workspace"

2. **Check File Watchers**
   ```json
   {
     "autocode.indexing.enableIncrementalUpdates": true
   }
   ```

### Performance Issues

#### Problem: Extension Running Slowly
**Symptoms:**
- Delayed responses to commands
- UI freezing or lagging
- High CPU/memory usage

**Solutions:**
1. **Optimize Settings**
   ```json
   {
     "autocode.model.maxTokens": 512,
     "autocode.indexing.maxConcurrentFiles": 5,
     "autocode.performance.maxConcurrentRequests": 2
   }
   ```

2. **Reduce Context Size**
   ```json
   {
     "autocode.context.maxItems": 10,
     "autocode.context.maxFileSize": 50000
   }
   ```

3. **Disable Features Temporarily**
   ```json
   {
     "autocode.webAccess.enabled": false,
     "autocode.indexing.autoIndexOnStartup": false
   }
   ```

### UI Issues

#### Problem: Commands Not Appearing
**Symptoms:**
- AutoCode commands missing from Command Palette
- Menu items not showing

**Solutions:**
1. **Restart VSCode**
   - Close and reopen VSCode
   - Check if extension is enabled

2. **Reinstall Extension**
   - Disable extension
   - Uninstall extension
   - Reinstall from marketplace or VSIX

3. **Check VSCode Version**
   - Ensure VSCode 1.60.0 or higher
   - Update VSCode if needed

#### Problem: Status Bar Items Missing
**Symptoms:**
- AutoCode status indicators not visible
- Status bar appears empty

**Solutions:**
1. **Enable Status Bar Items**
   ```json
   {
     "autocode.ui.showStatusBar": true,
     "autocode.ui.statusBar.showModelStatus": true,
     "autocode.ui.statusBar.showIndexStatus": true
   }
   ```

2. **Reset Status Bar**
   - Right-click status bar
   - Select "Reset Status Bar"

### Terminal Issues

#### Problem: Terminal Commands Not Executing
**Symptoms:**
- Agent mode can't run terminal commands
- "Terminal access denied" errors

**Solutions:**
1. **Check Terminal Settings**
   ```json
   {
     "autocode.terminal.enabled": true,
     "autocode.terminal.shell": "auto"
   }
   ```

2. **Verify Permissions**
   - Ensure VSCode has terminal access
   - Check system security settings

3. **Test Manual Terminal**
   - Open integrated terminal manually
   - Verify basic commands work

### Internet Access Issues

#### Problem: Web Access Not Working
**Symptoms:**
- "Unable to access internet" messages
- Outdated information in responses

**Solutions:**
1. **Check Network Connection**
   - Verify internet connectivity
   - Test with browser

2. **Configure Proxy Settings**
   ```json
   {
     "autocode.webAccess.proxy": "http://proxy.company.com:8080",
     "autocode.webAccess.timeout": 15000
   }
   ```

3. **Check Firewall Settings**
   - Allow VSCode through firewall
   - Check corporate network restrictions

## Advanced Troubleshooting

### Enable Debug Logging

1. **Enable Verbose Logging**
   ```json
   {
     "autocode.logging.level": "debug",
     "autocode.debug.enableVerboseLogging": true
   }
   ```

2. **View Logs**
   - Command Palette: "AutoCode: Show Logs"
   - Check VSCode Developer Console (`Help` → `Toggle Developer Tools`)

### Reset Extension State

1. **Clear All Data**
   - Command Palette: "AutoCode: Reset Extension"
   - Confirm when prompted

2. **Manual Reset**
   ```bash
   # Remove extension data (adjust path for your OS)
   rm -rf ~/.vscode/extensions/autocode-*
   rm -rf ~/.config/Code/User/globalStorage/autocode-*
   ```

### Performance Profiling

1. **Enable Performance Metrics**
   ```json
   {
     "autocode.debug.enablePerformanceMetrics": true
   }
   ```

2. **Monitor Resource Usage**
   - Task Manager (Windows)
   - Activity Monitor (macOS)
   - System Monitor (Linux)

## Error Messages

### Common Error Messages and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| "Model failed to load" | Missing/corrupt model files | Redownload model files |
| "Indexing timeout" | Large codebase | Increase timeout or exclude directories |
| "Context too large" | Too many context items | Reduce context size |
| "Network error" | Internet connectivity | Check network settings |
| "Permission denied" | File/folder permissions | Fix file permissions |

### Getting Help with Errors

1. **Copy Full Error Message**
   - Include stack trace if available
   - Note when error occurs

2. **Gather System Information**
   - OS version
   - VSCode version
   - Extension version
   - Node.js version

3. **Create Issue Report**
   - Use GitHub issue template
   - Include error details and system info
   - Describe steps to reproduce

## Recovery Procedures

### Complete Reset

1. **Backup Important Data**
   - Export conversations
   - Save custom configurations

2. **Uninstall Extension**
   - Remove from VSCode
   - Delete model files
   - Clear cache directories

3. **Fresh Installation**
   - Follow installation guide
   - Restore backed-up data

### Partial Reset

1. **Reset Specific Components**
   - "AutoCode: Clear Index" for indexing issues
   - "AutoCode: Reload Model" for model issues
   - "AutoCode: Clear Context" for context issues

## Prevention Tips

1. **Regular Maintenance**
   - Clear cache periodically
   - Update extension regularly
   - Monitor resource usage

2. **Optimal Configuration**
   - Start with default settings
   - Adjust based on system capabilities
   - Use workspace-specific settings

3. **System Health**
   - Keep adequate free disk space
   - Monitor memory usage
   - Update system regularly

## Getting Support

### Self-Help Resources
1. **Documentation** - Check all documentation files
2. **GitHub Issues** - Search existing issues
3. **Community Forums** - Join discussions

### Reporting Issues
1. **GitHub Issues** - For bugs and feature requests
2. **Discord Community** - For general help
3. **Email Support** - For urgent issues

### Information to Include
- Extension version
- VSCode version
- Operating system
- Error messages
- Steps to reproduce
- System specifications
