/**
 * Agent mode implementation
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { ModelService } from '../services/modelService';
import { IndexService } from '../services/indexService';

// Define agent action
export interface AgentAction {
    id: string;
    type: 'file_create' | 'file_update' | 'file_delete' | 'terminal_command';
    description: string;
    details: any;
    status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
    result?: string;
    error?: string;
    timestamp: number;
}

// Define agent session
export interface AgentSession {
    id: string;
    name: string;
    task: string;
    actions: AgentAction[];
    createdAt: number;
    updatedAt: number;
    status: 'in_progress' | 'completed' | 'failed';
}

/**
 * Agent mode implementation
 */
export class AgentMode {
    private modelService: ModelService;
    private indexService: IndexService;
    private context: vscode.ExtensionContext;
    private currentSession: AgentSession | null = null;
    private sessions: AgentSession[] = [];
    private webviewPanel: vscode.WebviewPanel | null = null;
    private disposables: vscode.Disposable[] = [];
    private terminal: vscode.Terminal | null = null;

    constructor(
        context: vscode.ExtensionContext,
        modelService: ModelService,
        indexService: IndexService
    ) {
        this.context = context;
        this.modelService = modelService;
        this.indexService = indexService;
        
        // Load sessions from storage
        this.loadSessions();
        
        // Register commands
        this.registerCommands();
    }

    /**
     * Start a new agent session
     * @param task The task description
     */
    public async startNewSession(task: string): Promise<void> {
        // Create a new session
        const session: AgentSession = {
            id: this.generateId(),
            name: `Agent ${new Date().toLocaleString()}`,
            task,
            actions: [],
            createdAt: Date.now(),
            updatedAt: Date.now(),
            status: 'in_progress'
        };
        
        // Set as current session
        this.currentSession = session;
        
        // Add to sessions
        this.sessions.push(session);
        
        // Save sessions
        this.saveSessions();
        
        // Show agent UI
        this.showAgentUI();
        
        // Generate plan
        await this.generatePlan();
    }

    /**
     * Continue an existing session
     * @param sessionId The session ID
     */
    public async continueSession(sessionId: string): Promise<void> {
        // Find the session
        const session = this.sessions.find(s => s.id === sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        
        // Set as current session
        this.currentSession = session;
        
        // Show agent UI
        this.showAgentUI();
    }

    /**
     * Generate a plan for the current task
     */
    private async generatePlan(): Promise<void> {
        if (!this.currentSession) {
            return;
        }
        
        try {
            // Prepare the prompt
            const prompt = `Task: ${this.currentSession.task}\n\nGenerate a step-by-step plan to accomplish this task. For each step, specify the action type (file_create, file_update, file_delete, terminal_command) and provide details.`;
            
            // Generate text
            const response = await this.modelService.generateText({
                prompt,
                maxTokens: 1024,
                temperature: 0.7
            });
            
            // Parse the response to extract actions
            // This is a simplified implementation
            const actions = this.parseActionsFromResponse(response.text);
            
            // Add actions to the session
            for (const action of actions) {
                this.currentSession.actions.push(action);
            }
            
            this.currentSession.updatedAt = Date.now();
            
            // Update UI
            this.updateAgentUI();
            
            // Save sessions
            this.saveSessions();
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to generate plan: ${error.message}`);
        }
    }

    /**
     * Parse actions from the model response
     * @param text The response text
     */
    private parseActionsFromResponse(text: string): AgentAction[] {
        // This is a simplified implementation
        // In a real implementation, you would use a more sophisticated parsing approach
        
        const actions: AgentAction[] = [];
        
        // Split the text into lines
        const lines = text.split('\n');
        
        // Look for action descriptions
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Check if the line contains an action type
            if (line.includes('file_create') || line.includes('file_update') || line.includes('file_delete') || line.includes('terminal_command')) {
                // Extract action type
                let type: AgentAction['type'] = 'file_create';
                if (line.includes('file_update')) {
                    type = 'file_update';
                } else if (line.includes('file_delete')) {
                    type = 'file_delete';
                } else if (line.includes('terminal_command')) {
                    type = 'terminal_command';
                }
                
                // Extract description
                const description = line.replace(/file_create|file_update|file_delete|terminal_command/g, '').trim();
                
                // Create action
                const action: AgentAction = {
                    id: this.generateId(),
                    type,
                    description,
                    details: {},
                    status: 'pending',
                    timestamp: Date.now()
                };
                
                // Add details based on type
                if (type === 'file_create' || type === 'file_update') {
                    // Look for file path and content in the next lines
                    let filePath = '';
                    let fileContent = '';
                    
                    for (let j = i + 1; j < lines.length && j < i + 10; j++) {
                        const nextLine = lines[j].trim();
                        
                        if (nextLine.startsWith('Path:')) {
                            filePath = nextLine.substring(5).trim();
                        } else if (nextLine.startsWith('Content:')) {
                            // Collect content until the next action or end
                            let k = j + 1;
                            while (k < lines.length && !lines[k].includes('file_create') && !lines[k].includes('file_update') && !lines[k].includes('file_delete') && !lines[k].includes('terminal_command')) {
                                fileContent += lines[k] + '\n';
                                k++;
                            }
                            break;
                        }
                    }
                    
                    action.details = {
                        filePath,
                        fileContent
                    };
                } else if (type === 'file_delete') {
                    // Look for file path in the next lines
                    for (let j = i + 1; j < lines.length && j < i + 5; j++) {
                        const nextLine = lines[j].trim();
                        
                        if (nextLine.startsWith('Path:')) {
                            action.details = {
                                filePath: nextLine.substring(5).trim()
                            };
                            break;
                        }
                    }
                } else if (type === 'terminal_command') {
                    // Look for command in the next lines
                    for (let j = i + 1; j < lines.length && j < i + 5; j++) {
                        const nextLine = lines[j].trim();
                        
                        if (nextLine.startsWith('Command:')) {
                            action.details = {
                                command: nextLine.substring(8).trim()
                            };
                            break;
                        }
                    }
                }
                
                actions.push(action);
            }
        }
        
        // If no actions were found, create a placeholder action
        if (actions.length === 0) {
            actions.push({
                id: this.generateId(),
                type: 'terminal_command',
                description: 'Analyze the task',
                details: {
                    command: 'echo "Analyzing task..."'
                },
                status: 'pending',
                timestamp: Date.now()
            });
        }
        
        return actions;
    }

    /**
     * Approve an action
     * @param actionId The action ID
     */
    public async approveAction(actionId: string): Promise<void> {
        if (!this.currentSession) {
            return;
        }
        
        // Find the action
        const action = this.currentSession.actions.find(a => a.id === actionId);
        if (!action) {
            return;
        }
        
        // Update status
        action.status = 'approved';
        this.currentSession.updatedAt = Date.now();
        
        // Update UI
        this.updateAgentUI();
        
        // Save sessions
        this.saveSessions();
        
        // Execute the action
        await this.executeAction(action);
    }

    /**
     * Reject an action
     * @param actionId The action ID
     */
    public rejectAction(actionId: string): void {
        if (!this.currentSession) {
            return;
        }
        
        // Find the action
        const action = this.currentSession.actions.find(a => a.id === actionId);
        if (!action) {
            return;
        }
        
        // Update status
        action.status = 'rejected';
        this.currentSession.updatedAt = Date.now();
        
        // Update UI
        this.updateAgentUI();
        
        // Save sessions
        this.saveSessions();
    }

    /**
     * Execute an action
     * @param action The action to execute
     */
    private async executeAction(action: AgentAction): Promise<void> {
        try {
            switch (action.type) {
                case 'file_create':
                    await this.executeFileCreate(action);
                    break;
                case 'file_update':
                    await this.executeFileUpdate(action);
                    break;
                case 'file_delete':
                    await this.executeFileDelete(action);
                    break;
                case 'terminal_command':
                    await this.executeTerminalCommand(action);
                    break;
            }
            
            // Update status
            action.status = 'completed';
            if (this.currentSession) {
                this.currentSession.updatedAt = Date.now();
            }
            
            // Update UI
            this.updateAgentUI();
            
            // Save sessions
            this.saveSessions();
        } catch (error: any) {
            // Update status
            action.status = 'failed';
            action.error = error.message;
            if (this.currentSession) {
                this.currentSession.updatedAt = Date.now();
            }
            
            // Update UI
            this.updateAgentUI();
            
            // Save sessions
            this.saveSessions();
            
            // Show error message
            vscode.window.showErrorMessage(`Failed to execute action: ${error.message}`);
        }
    }

    /**
     * Execute a file create action
     * @param action The action to execute
     */
    private async executeFileCreate(action: AgentAction): Promise<void> {
        const { filePath, fileContent } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Create the directory if it doesn't exist
        const directory = path.dirname(fullPath);
        if (!fs.existsSync(directory)) {
            fs.mkdirSync(directory, { recursive: true });
        }
        
        // Check if the file already exists
        if (fs.existsSync(fullPath)) {
            throw new Error(`File already exists: ${filePath}`);
        }
        
        // Write the file
        fs.writeFileSync(fullPath, fileContent || '');
        
        // Update the action result
        action.result = `Created file: ${filePath}`;
    }

    /**
     * Execute a file update action
     * @param action The action to execute
     */
    private async executeFileUpdate(action: AgentAction): Promise<void> {
        const { filePath, fileContent } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Check if the file exists
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        // Write the file
        fs.writeFileSync(fullPath, fileContent || '');
        
        // Update the action result
        action.result = `Updated file: ${filePath}`;
    }

    /**
     * Execute a file delete action
     * @param action The action to execute
     */
    private async executeFileDelete(action: AgentAction): Promise<void> {
        const { filePath } = action.details;
        
        if (!filePath) {
            throw new Error('File path is required');
        }
        
        // Get the workspace folder
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }
        
        // Create the full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, filePath);
        
        // Check if the file exists
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        // Delete the file
        fs.unlinkSync(fullPath);
        
        // Update the action result
        action.result = `Deleted file: ${filePath}`;
    }

    /**
     * Execute a terminal command action
     * @param action The action to execute
     */
    private async executeTerminalCommand(action: AgentAction): Promise<void> {
        const { command } = action.details;
        
        if (!command) {
            throw new Error('Command is required');
        }
        
        // Create terminal if it doesn't exist
        if (!this.terminal) {
            this.terminal = vscode.window.createTerminal('AutoCode Agent');
        }
        
        // Show the terminal
        this.terminal.show();
        
        // Execute the command
        this.terminal.sendText(command);
        
        // Update the action result
        action.result = `Executed command: ${command}`;
        
        // Wait for a short time to allow the command to start
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    /**
     * Show the agent UI
     */
    private showAgentUI(): void {
        // Check if panel already exists
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }
        
        // Create webview panel
        this.webviewPanel = vscode.window.createWebviewPanel(
            'autocode.agent',
            'AutoCode Agent',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );
        
        // Set webview content
        this.updateAgentUI();
        
        // Handle messages from webview
        this.webviewPanel.webview.onDidReceiveMessage(
            async (message) => {
                if (message.command === 'approveAction') {
                    await this.approveAction(message.actionId);
                } else if (message.command === 'rejectAction') {
                    this.rejectAction(message.actionId);
                } else if (message.command === 'newSession') {
                    const task = await vscode.window.showInputBox({
                        prompt: 'Enter the task description',
                        placeHolder: 'e.g., Create a React component for a login form'
                    });
                    
                    if (task) {
                        await this.startNewSession(task);
                    }
                } else if (message.command === 'continueSession') {
                    await this.continueSession(message.sessionId);
                } else if (message.command === 'renameSession') {
                    this.renameSession(message.sessionId, message.name);
                } else if (message.command === 'deleteSession') {
                    this.deleteSession(message.sessionId);
                }
            },
            undefined,
            this.disposables
        );
        
        // Handle panel disposal
        this.webviewPanel.onDidDispose(
            () => {
                this.webviewPanel = null;
            },
            null,
            this.disposables
        );
    }

    /**
     * Update the agent UI
     */
    private updateAgentUI(): void {
        if (!this.webviewPanel) {
            return;
        }
        
        // Create HTML content
        const html = this.getAgentHtml();
        
        // Set webview content
        this.webviewPanel.webview.html = html;
    }

    /**
     * Get the agent HTML
     */
    private getAgentHtml(): string {
        // This is a placeholder implementation
        // In a real implementation, you would create a more sophisticated UI
        
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AutoCode Agent</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 0;
                        margin: 0;
                    }
                    .container {
                        display: flex;
                        height: 100vh;
                    }
                    .sidebar {
                        width: 250px;
                        border-right: 1px solid var(--vscode-panel-border);
                        overflow-y: auto;
                        padding: 10px;
                    }
                    .main {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        overflow-y: auto;
                        padding: 10px;
                    }
                    .session {
                        padding: 5px;
                        margin-bottom: 5px;
                        cursor: pointer;
                        border-radius: 3px;
                    }
                    .session:hover {
                        background-color: var(--vscode-list-hoverBackground);
                    }
                    .session.active {
                        background-color: var(--vscode-list-activeSelectionBackground);
                        color: var(--vscode-list-activeSelectionForeground);
                    }
                    .new-session {
                        margin-top: 10px;
                        padding: 5px;
                        text-align: center;
                        cursor: pointer;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border-radius: 3px;
                    }
                    .new-session:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    .task {
                        margin-bottom: 20px;
                    }
                    .action {
                        margin-bottom: 15px;
                        padding: 10px;
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 5px;
                    }
                    .action-header {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                    }
                    .action-type {
                        font-weight: bold;
                    }
                    .action-status {
                        font-style: italic;
                    }
                    .action-description {
                        margin-bottom: 5px;
                    }
                    .action-details {
                        margin-bottom: 5px;
                        padding: 5px;
                        background-color: var(--vscode-editor-lineHighlightBackground);
                        border-radius: 3px;
                        font-family: monospace;
                        white-space: pre-wrap;
                    }
                    .action-result {
                        margin-top: 5px;
                        font-style: italic;
                    }
                    .action-error {
                        margin-top: 5px;
                        color: var(--vscode-errorForeground);
                    }
                    .action-buttons {
                        display: flex;
                        justify-content: flex-end;
                        margin-top: 5px;
                    }
                    button {
                        padding: 5px 10px;
                        margin-left: 5px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 3px;
                        cursor: pointer;
                    }
                    button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    button:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                    .pending {
                        border-left: 3px solid var(--vscode-editorInfo-foreground);
                    }
                    .approved {
                        border-left: 3px solid var(--vscode-editorWarning-foreground);
                    }
                    .completed {
                        border-left: 3px solid var(--vscode-editorGreen-foreground);
                    }
                    .rejected {
                        border-left: 3px solid var(--vscode-editorHint-foreground);
                    }
                    .failed {
                        border-left: 3px solid var(--vscode-editorError-foreground);
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="sidebar">
                        <h3>Sessions</h3>
                        ${this.sessions.map(session => `
                            <div class="session ${this.currentSession && session.id === this.currentSession.id ? 'active' : ''}" data-id="${session.id}">
                                ${session.name}
                            </div>
                        `).join('')}
                        <div class="new-session">New Session</div>
                    </div>
                    <div class="main">
                        ${this.currentSession ? `
                            <div class="task">
                                <h2>Task</h2>
                                <p>${this.currentSession.task}</p>
                            </div>
                            <h2>Actions</h2>
                            ${this.currentSession.actions.map(action => `
                                <div class="action ${action.status}">
                                    <div class="action-header">
                                        <div class="action-type">${action.type}</div>
                                        <div class="action-status">${action.status}</div>
                                    </div>
                                    <div class="action-description">${action.description}</div>
                                    <div class="action-details">
                                        ${action.type === 'file_create' || action.type === 'file_update' ? `
                                            Path: ${action.details.filePath || 'N/A'}
                                            ${action.details.fileContent ? `\nContent:\n${action.details.fileContent}` : ''}
                                        ` : action.type === 'file_delete' ? `
                                            Path: ${action.details.filePath || 'N/A'}
                                        ` : action.type === 'terminal_command' ? `
                                            Command: ${action.details.command || 'N/A'}
                                        ` : ''}
                                    </div>
                                    ${action.result ? `<div class="action-result">${action.result}</div>` : ''}
                                    ${action.error ? `<div class="action-error">${action.error}</div>` : ''}
                                    ${action.status === 'pending' ? `
                                        <div class="action-buttons">
                                            <button class="approve-button" data-id="${action.id}">Approve</button>
                                            <button class="reject-button" data-id="${action.id}">Reject</button>
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        ` : '<p>No session selected. Create a new session or select an existing one.</p>'}
                    </div>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    // Handle session click
                    document.querySelectorAll('.session').forEach(element => {
                        element.addEventListener('click', () => {
                            const sessionId = element.dataset.id;
                            
                            vscode.postMessage({
                                command: 'continueSession',
                                sessionId
                            });
                        });
                    });
                    
                    // Handle new session click
                    document.querySelector('.new-session').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'newSession'
                        });
                    });
                    
                    // Handle approve button click
                    document.querySelectorAll('.approve-button').forEach(element => {
                        element.addEventListener('click', () => {
                            const actionId = element.dataset.id;
                            
                            vscode.postMessage({
                                command: 'approveAction',
                                actionId
                            });
                        });
                    });
                    
                    // Handle reject button click
                    document.querySelectorAll('.reject-button').forEach(element => {
                        element.addEventListener('click', () => {
                            const actionId = element.dataset.id;
                            
                            vscode.postMessage({
                                command: 'rejectAction',
                                actionId
                            });
                        });
                    });
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Rename a session
     * @param sessionId The session ID
     * @param name The new name
     */
    private renameSession(sessionId: string, name: string): void {
        // Find the session
        const session = this.sessions.find(s => s.id === sessionId);
        if (!session) {
            return;
        }
        
        // Update the name
        session.name = name;
        session.updatedAt = Date.now();
        
        // Save sessions
        this.saveSessions();
        
        // Update UI
        this.updateAgentUI();
    }

    /**
     * Delete a session
     * @param sessionId The session ID
     */
    private deleteSession(sessionId: string): void {
        // Find the session index
        const index = this.sessions.findIndex(s => s.id === sessionId);
        if (index === -1) {
            return;
        }
        
        // Remove the session
        this.sessions.splice(index, 1);
        
        // Update current session if needed
        if (this.currentSession && this.currentSession.id === sessionId) {
            this.currentSession = this.sessions.length > 0 ? this.sessions[0] : null;
        }
        
        // Save sessions
        this.saveSessions();
        
        // Update UI
        this.updateAgentUI();
    }

    /**
     * Load sessions from storage
     */
    private loadSessions(): void {
        try {
            // Get sessions from storage
            const sessionsJson = this.context.globalState.get<string>('autocode.agentSessions');
            if (sessionsJson) {
                this.sessions = JSON.parse(sessionsJson);
            }
        } catch (error) {
            console.error('Failed to load agent sessions:', error);
        }
    }

    /**
     * Save sessions to storage
     */
    private saveSessions(): void {
        try {
            // Save sessions to storage
            const sessionsJson = JSON.stringify(this.sessions);
            this.context.globalState.update('autocode.agentSessions', sessionsJson);
        } catch (error) {
            console.error('Failed to save agent sessions:', error);
        }
    }

    /**
     * Generate a unique ID
     */
    private generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    /**
     * Register commands
     */
    private registerCommands(): void {
        // Register start agent command
        const startAgentCommand = vscode.commands.registerCommand('autocode.startAgent', async () => {
            const task = await vscode.window.showInputBox({
                prompt: 'Enter the task description',
                placeHolder: 'e.g., Create a React component for a login form'
            });
            
            if (task) {
                await this.startNewSession(task);
            }
        });
        
        // Add to disposables
        this.disposables.push(startAgentCommand);
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.disposables = [];
        
        // Dispose of webview panel
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
            this.webviewPanel = null;
        }
        
        // Dispose of terminal
        if (this.terminal) {
            this.terminal.dispose();
            this.terminal = null;
        }
    }
}
